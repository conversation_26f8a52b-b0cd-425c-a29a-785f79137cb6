# LLaVA: Large Language and Vision Assistant

## What is LLaVA?

LLaVA (Large Language and Vision Assistant) is a multimodal AI framework that connects a vision encoder with a language model to enable visual understanding and reasoning capabilities. It allows the model to process both text and images, enabling it to answer questions about images, describe visual content, and perform complex reasoning tasks that require visual understanding.

LLaVA represents a significant advancement in multimodal AI systems, achieving GPT-4 level capabilities in visual instruction following through a novel training approach called "visual instruction tuning." The framework has evolved through several versions, with LLaVA-1.5 and LLaVA-NeXT being the most recent iterations, offering improved performance across various benchmarks.

## Architecture and Components

LLaVA consists of three main components:

1. **Vision Encoder**: A pre-trained CLIP (Contrastive Language-Image Pre-training) vision model that processes images and extracts visual features. LLaVA-1.5 uses CLIP ViT-L/14 with 336px resolution.

2. **Vision-Language Connector**: A projector module that aligns the visual features from the vision encoder with the language model's embedding space. LLaVA-1.5 uses a two-layer MLP (Multi-Layer Perceptron) with GELU activation.

3. **Language Model**: A large language model (LLM) that generates text responses based on the combined visual and textual inputs. LLaVA supports various LLMs including Vicuna, LLaMA-2, and more recently, LLaMA-3 and Qwen models.

### Architecture Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Vision Encoder │────▶│ Vision-Language │────▶│ Language Model  │
│  (CLIP ViT-L/14)│     │   Connector     │     │ (Vicuna/LLaMA)  │
│                 │     │   (MLP)         │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        ▲                                               │
        │                                               │
        │                                               ▼
┌─────────────────┐                           ┌─────────────────┐
│                 │                           │                 │
│  Input Image    │                           │  Text Response  │
│                 │                           │                 │
└─────────────────┘                           └─────────────────┘
```

## Data Input Requirements

LLaVA accepts the following inputs:

1. **Images**:
   - **Supported formats**: JPEG, PNG, and other common image formats
   - **Resolution**:
     - Optimized for 336x336 pixels in LLaVA-1.5 (earlier versions used 224x224)
     - LLaVA-NeXT can process up to 4x more pixels than previous versions (1344x1344)
   - **Aspect ratio handling**:
     - LLaVA-1.5 uses padding (`image_aspect_ratio: pad`) to preserve aspect ratios instead of cropping
     - This approach reduces hallucination by ensuring no visual information is lost
   - **Image preprocessing**:
     - Images are normalized using CLIP's mean and standard deviation values
     - For non-square images, padding is applied to make them square while preserving content
   - **Multiple image support**:
     - LLaVA-NeXT supports multiple images in a single conversation
     - Images can be referenced in context throughout the conversation
   - **Image tokens**: Images are represented as special tokens (`<image>`) in the input sequence

2. **Text Prompts**:
   - **Question formats**:
     - Open-ended questions about image content
     - Specific requests for information visible in the image
     - Instructions for analysis or reasoning about visual elements
   - **Conversation structure**:
     - Supports multi-turn conversations with context maintenance
     - Different conversation templates based on the base LLM (Vicuna, LLaMA-2, etc.)
   - **System prompts**:
     - Configurable system messages to guide model behavior
     - Default system prompt defines the assistant as "helpful, detailed, and polite"
   - **Special tokens**:
     - Uses model-specific separator tokens (e.g., `###` for Vicuna, `<s>` and `</s>` for LLaMA-2)
     - Image tokens can be wrapped with start/end markers (`<im_start>`, `<im_end>`)
   - **Input length**:
     - Maximum input length determined by the base LLM's context window
     - Typically 2048 tokens for LLaVA-1.5 (including both text and image tokens)

## Feature Generation Process

When an image is provided to LLaVA, the following detailed process occurs:

1. **Image Preprocessing**:
   - **Resizing**:
     - Images are resized to the target resolution (336x336 pixels in LLaVA-1.5)
     - For LLaVA-NeXT, higher resolutions up to 1344x1344 are supported
   - **Normalization**:
     - Pixel values are normalized using CLIP's mean [0.48145466, 0.4578275, 0.40821073] and standard deviation [0.26862954, 0.26130258, 0.27577711]
     - Values are scaled from [0,255] to the range expected by the vision encoder
   - **Aspect Ratio Handling**:
     - The `image_aspect_ratio` parameter controls how non-square images are handled
     - In `pad` mode, images are padded with a background color (typically gray) to preserve all visual information
     - The `unpad_image` function in the codebase handles the reverse process when needed

2. **Visual Feature Extraction**:
   - **CLIP Processing**:
     - The image is passed through the CLIP ViT-L/14 vision encoder
     - The encoder divides the image into 14×14 pixel patches, resulting in 576 patches for a 336×336 image
     - Each patch is processed through the transformer architecture of CLIP
   - **Feature Selection**:
     - Features are extracted from a specific layer of the vision encoder (controlled by `mm_vision_select_layer`, default is the last layer)
     - The `mm_vision_select_feature` parameter determines which features to use:
       - `patch`: Uses only patch features (excluding the CLS token)
       - `cls_patch`: Uses both CLS token and patch features
     - For a 336×336 image, this results in 576 feature vectors, each with dimension 1024 (CLIP ViT-L/14's hidden size)
   - **Spatial Information**:
     - The 2D spatial arrangement of patches is preserved in the feature representation
     - This allows the model to understand spatial relationships in the image

3. **Feature Projection**:
   - **Connector Architecture**:
     - The extracted visual features are passed through the vision-language connector
     - For LLaVA-1.5, this is a two-layer MLP with GELU activation (`mlp2x_gelu`)
     - The first layer projects from CLIP's hidden size (1024) to the LLM's hidden size (e.g., 4096 for Vicuna-13B)
     - The GELU activation adds non-linearity
     - The second layer maintains the LLM's hidden size dimension
   - **Dimension Alignment**:
     - This projection aligns the visual feature space with the language model's embedding space
     - After projection, each patch feature has the same dimension as a text token embedding
   - **Implementation Details**:
     - The projection is implemented in `multimodal_projector/builder.py`
     - Different projector types can be selected via the `mm_projector_type` parameter

4. **Token Integration**:
   - **Tokenization Process**:
     - Text inputs are tokenized using the language model's tokenizer
     - Special image tokens (`<image>`) in the text are identified
     - The `tokenizer_image_token` function handles this process
   - **Embedding Combination**:
     - Text tokens are converted to embeddings via the LLM's embedding layer
     - The projected visual features replace the image token embeddings
     - The `prepare_inputs_labels_for_multimodal` method in `llava_arch.py` handles this integration
   - **Sequence Construction**:
     - A complete sequence is formed with text embeddings and visual embeddings
     - Position IDs and attention masks are adjusted to account for the visual tokens
     - For multiple images, each image's features are inserted at the corresponding image token position

5. **Response Generation**:
   - **Forward Pass**:
     - The combined sequence of embeddings is passed through the language model
     - The model's attention mechanism can attend to both text and visual tokens
     - This allows cross-modal reasoning between visual and textual information
   - **Autoregressive Generation**:
     - The model generates text tokens autoregressively (one at a time)
     - Each new token is influenced by both the input text and visual features
     - Generation parameters like temperature and top-p sampling control the output style
   - **Post-processing**:
     - Generated tokens are converted back to text
     - The conversation manager formats the response according to the chosen conversation template

## Training Methodology

LLaVA's training process consists of two main stages, each with specific objectives, data, and technical configurations:

### 1. Feature Alignment Stage (Pretraining)

- **Purpose**:
  - Connect the frozen vision encoder to the frozen language model
  - Establish a shared embedding space between visual and textual modalities
  - Learn to translate visual concepts into language representations

- **Data**:
  - 558K subset of LAION-CC-SBU dataset with BLIP captions
  - Concept-balanced distribution for better coverage of visual concepts
  - Each sample consists of an image and its corresponding caption
  - Images are processed at 336x336 resolution in LLaVA-1.5

- **Training Configuration**:
  - **Batch Size**: 256 global batch size
  - **Learning Rate**: 1e-3 with cosine learning rate schedule
  - **Epochs**: 1 epoch is sufficient for convergence
  - **Max Length**: 2048 tokens
  - **Weight Decay**: 0
  - **Optimizer**: AdamW with β₁=0.9, β₂=0.999, ε=1e-8

- **Training Strategy**:
  - Only the vision-language connector (MLP projector) is trained
  - Both the vision encoder (CLIP) and language model (Vicuna/LLaMA) are kept frozen
  - The model learns to predict the caption given the image features
  - Loss is computed only on the text tokens, not on the image tokens
  - DeepSpeed ZeRO-2 optimization is used for efficient training

- **Implementation Details**:
  - Training script: `scripts/v1_5/pretrain.sh`
  - The connector is initialized randomly before training
  - Gradient checkpointing is used to reduce memory usage
  - Duration: ~5.5 hours for LLaVA-1.5-13B on 8x A100 (80G) GPUs

### 2. Visual Instruction Tuning Stage (Fine-tuning)

- **Purpose**:
  - Teach the model to follow multimodal instructions
  - Enable complex reasoning about visual content
  - Align the model with human preferences for helpful, accurate responses

- **Data**:
  - **Academic VQA Data** (~515K samples):
    - COCO: Object detection and captioning dataset
    - GQA: Compositional visual reasoning questions
    - OCR-VQA: Questions about text in images
    - TextVQA: Reading comprehension in images
    - VisualGenome: Dense annotations of objects, attributes, and relationships

  - **GPT-4 Generated Data** (150K samples):
    - Conversation (58K): Multi-turn dialogues about images
    - Detailed Description (23K): Comprehensive image descriptions
    - Complex Reasoning (77K): Questions requiring in-depth analysis
    - Generated using GPT-4 with carefully designed prompts
    - Diverse question types covering various reasoning skills

- **Training Configuration**:
  - **Batch Size**: 128 global batch size
  - **Learning Rate**: 2e-5 with cosine learning rate schedule
  - **Epochs**: 1 epoch
  - **Max Length**: 2048 tokens
  - **Weight Decay**: 0
  - **Optimizer**: AdamW with β₁=0.9, β₂=0.999, ε=1e-8

- **Training Strategy**:
  - Full fine-tuning of the language model and projector
  - Vision encoder (CLIP) remains frozen
  - Mixed precision training (bfloat16) for efficiency
  - DeepSpeed ZeRO-3 optimization for memory efficiency
  - LoRA (Low-Rank Adaptation) option for resource-constrained environments
  - `group_by_modality_length=True` to batch similar-length inputs together

- **Implementation Details**:
  - Training script: `scripts/v1_5/finetune.sh` (full fine-tuning)
  - Alternative: `scripts/v1_5/finetune_lora.sh` (LoRA fine-tuning)
  - Uses the pretrained projector from the first stage
  - Duration: ~20 hours for LLaVA-1.5-13B on 8x A100 (80G) GPUs
  - For LoRA: Can be trained on consumer GPUs (e.g., 8x RTX 3090)

## Model Components in Detail

### Vision Encoder

- **Architecture**:
  - CLIP ViT-L/14 (Vision Transformer Large with 14×14 pixel patches)
  - 24 transformer layers with 16 attention heads each
  - Layer normalization and residual connections throughout
  - Total parameters: ~304M for the vision encoder alone

- **Input Processing**:
  - Resolution: 336×336 pixels in LLaVA-1.5 (224×224 in earlier versions)
  - Patch size: 14×14 pixels, resulting in 24×24=576 patches per image
  - Each patch is linearly projected to the embedding dimension (1024)
  - Position embeddings are added to provide spatial information

- **Feature Extraction**:
  - Hidden size: 1024 dimensions per token
  - Feature selection controlled by `mm_vision_select_layer` (default: -1, last layer)
  - Feature type controlled by `mm_vision_select_feature` (default: "patch")
    - "patch": Uses only patch features (576 vectors of dimension 1024)
    - "cls_patch": Uses CLS token + patch features (577 vectors of dimension 1024)

- **Implementation Details**:
  - Uses the Hugging Face `CLIPVisionModel` class
  - Implemented in `llava/model/multimodal_encoder/clip_encoder.py`
  - Loaded with `delay_load=True` option to save memory when not in use
  - Frozen during both training stages (parameters not updated)
  - Supports multi-scale feature extraction in LLaVA-NeXT via `CLIPVisionTowerS2` class

- **Variants**:
  - LLaVA-1.5: CLIP ViT-L/14 at 336×336 resolution
  - LLaVA-NeXT: Supports higher resolutions up to 1344×1344
  - S2 variant: Processes images at multiple scales for better detail recognition

### Vision-Language Connector

- **Architecture**:
  - Two-layer MLP with GELU activation (`mlp2x_gelu`)
  - First layer: Linear projection from CLIP hidden size to LLM hidden size
  - GELU activation function: Smooth approximation of ReLU with better gradient properties
  - Second layer: Linear projection maintaining LLM hidden size
  - No bias terms in the linear layers
  - No dropout or other regularization techniques

- **Dimensions**:
  - Input dimension: CLIP's hidden size (1024 for ViT-L/14)
  - Hidden dimension: Same as LLM's hidden size (4096 for Vicuna-13B)
  - Output dimension: Same as LLM's hidden size (4096 for Vicuna-13B)
  - Total parameters: ~21M for 13B models (1024×4096 + 4096×4096)

- **Variants**:
  - `linear`: Simple linear projection (used in earlier versions)
  - `mlp2x_gelu`: Two-layer MLP with GELU (default in LLaVA-1.5)
  - `identity`: No projection, used for debugging or when dimensions already match

- **Implementation Details**:
  - Implemented in `llava/model/multimodal_projector/builder.py`
  - Initialized randomly before pretraining
  - Only component trained during the feature alignment stage
  - Fine-tuned along with the language model during instruction tuning
  - Can be extracted and saved separately for modular model composition

- **Training Characteristics**:
  - Learns to map visual features to a space compatible with language embeddings
  - Acts as a translation layer between visual and linguistic representations
  - Relatively small but crucial component for cross-modal understanding

### Language Model

- **Architecture**:
  - Decoder-only transformer architecture
  - Multiple supported base models:
    - Vicuna-1.5 (default for LLaVA-1.5)
    - LLaMA-2
    - Mistral
    - LLaMA-3 (in LLaVA-NeXT)
    - Qwen-1.5 (in LLaVA-NeXT)

- **Model Variants and Sizes**:
  - 7B: ~7 billion parameters, suitable for consumer GPUs
  - 13B: ~13 billion parameters, recommended for best performance/resource trade-off
  - 34B: ~34 billion parameters, available in LLaVA-NeXT
  - 72B/110B: Largest variants available in LLaVA-NeXT with Qwen-1.5

- **Technical Specifications**:
  - Hidden size: 4096 for 7B/13B models, larger for bigger models
  - Attention heads: 32 for 13B models, varies by model size
  - Layers: 40 for 13B models, varies by model size
  - Vocabulary size: ~32K tokens for most models
  - Context length: 2048 tokens for LLaVA-1.5, extended in newer versions

- **Tokenizer**:
  - Uses the base LLM's tokenizer (e.g., LLaMA tokenizer)
  - Special tokens added for multimodal functionality:
    - `DEFAULT_IMAGE_TOKEN`: Represents an image in the input
    - `DEFAULT_IM_START_TOKEN` and `DEFAULT_IM_END_TOKEN`: Optional markers around images
    - `DEFAULT_IMAGE_PATCH_TOKEN`: Represents individual image patches (optional)
  - Tokenization handled by `tokenizer_image_token` function

- **Conversation Management**:
  - Implemented in `llava/conversation.py`
  - Supports multiple conversation templates:
    - Vicuna-style: Uses `###` as separator
    - LLaMA-2-style: Uses `<s>` and `</s>` with system prompt formatting
    - MPT-style: Uses specific formatting for MPT models
    - Plain: Minimal formatting for certain applications
  - Conversation history maintained as a list of role-message pairs
  - System prompts define the assistant's behavior and capabilities

## Evaluation and Benchmarks

LLaVA-1.5 is evaluated on 12 diverse benchmarks:

1. **VQAv2**: General visual question answering
2. **GQA**: Compositional visual reasoning
3. **VisWiz**: Visual questions from visually impaired users
4. **ScienceQA**: Multimodal science question answering
5. **TextVQA**: Questions about text in images
6. **POPE**: Object hallucination evaluation
7. **MME**: Multimodal evaluation suite
8. **MMBench**: Comprehensive multimodal benchmark
9. **SEED-Bench**: Spatial, temporal, and causal understanding
10. **LLaVA-Bench-in-the-Wild**: Real-world visual questions
11. **MM-Vet**: Multimodal reasoning and knowledge
12. **MMBench-CN**: Chinese multimodal benchmark

## Use Cases and Applications

LLaVA can be used for a wide range of applications:

1. **Visual Question Answering**: Answer questions about image content
2. **Image Description**: Generate detailed descriptions of images
3. **Visual Reasoning**: Perform complex reasoning about visual scenes
4. **Multimodal Chat**: Engage in conversations that involve both text and images
5. **Accessibility**: Assist visually impaired users in understanding visual content
6. **Content Analysis**: Analyze and extract information from visual media
7. **Educational Tools**: Explain visual concepts and answer educational questions
8. **Creative Applications**: Generate creative content based on visual prompts

## Deployment Options

LLaVA offers several deployment options:

1. **Gradio Web UI**: Interactive web interface for chat with images
2. **CLI Interface**: Command-line interface for quick testing
3. **API Server**: Controller-worker architecture for scalable deployment
4. **Quantized Inference**: 4-bit and 8-bit quantization for reduced memory footprint
5. **Multi-GPU Support**: Distributed inference across multiple GPUs
6. **Integration with Other Tools**: Compatible with llama.cpp, AutoGen, etc.

## Limitations and Considerations

1. **Hallucination**: Like other multimodal models, LLaVA may occasionally hallucinate details not present in images
2. **Computational Requirements**: Full-precision models require significant GPU memory
3. **Context Length**: Limited by the underlying language model's context window
4. **Fine-grained Understanding**: May struggle with very detailed or specialized visual tasks
5. **Ethical Considerations**: Inherits biases from training data and base models

## Recent Developments

The LLaVA framework continues to evolve with recent advancements:

1. **LLaVA-NeXT**: Supports LLaMA-3 and Qwen models with improved performance
2. **Video Support**: Zero-shot transfer to video understanding
3. **Higher Resolution**: Processing more pixels for better detail recognition
4. **LLaVA-Plus**: Integration with external tools and skills
5. **LLaVA-Interactive**: Support for image chat, segmentation, generation, and editing

## Getting Started

To use LLaVA, you can:

1. Install the package: `pip install -e .`
2. Download pretrained weights from the Model Zoo
3. Run the Gradio web interface or CLI for inference
4. Fine-tune on custom data using the provided scripts

For more detailed instructions, refer to the main README.md and documentation.
