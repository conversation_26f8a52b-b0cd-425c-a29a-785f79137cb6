{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5c86cbbc-1efb-4ac2-b5c4-f9f06fc0d3d8", "metadata": {}, "outputs": [], "source": ["import os\n", "from tqdm import tqdm\n", "from itertools import chain\n", "import pandas as pd\n", "import numpy as np\n", "import random, torch\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "sns.set(style = 'white', font_scale=1.5)"]}, {"cell_type": "markdown", "id": "6788a7d7-36fc-47bc-9fd9-a7d8d638176e", "metadata": {}, "source": ["## 1. Importing Compass\n", "#### Installation\n", "Clone the repository and install the required dependencies:\n", "```bash\n", "git clone https://github.com/mims-harvard/COMPASS.git\n", "cd Immune-compass\n", "pip install -r requirements.txt\n", "```\n", "\n", "#### Adding Compass to Your Environment\n", "Before importing compass, add it to your Python path:"]}, {"cell_type": "code", "execution_count": 2, "id": "359e1153-75b7-43db-bca9-04bde4dea0b7", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.insert(0, '/home/<USER>/Research/COMPASS/')\n", "from compass import PreTrainer, FineTuner, loadcompass"]}, {"cell_type": "markdown", "id": "b5b4e47c-d813-4816-94fd-ff91c4fd1b35", "metadata": {}, "source": ["## 2. Making Predictions with a Compass Model\n"]}, {"cell_type": "markdown", "id": "0220308f-9347-43f9-8227-057f5ae47682", "metadata": {}, "source": ["You can download all available Compass fine-tuned models [here](https://www.immuno-compass.com/download/) for prediction.\n", "\n", "The input `df_tpm` is gene expression tabular data. Please refer [here](https://www.immuno-compass.com/help/index.html#section1) for details on generating input data. The first column represents the cancer code, while the remaining 15,672 columns correspond to genes. Each row represents one patient. An example input file can be downloaded [here](https://www.immuno-compass.com/download/other/compass_gide_tpm.tsv).\n", "\n", "The output `df_pred` contains two columns, where `0` indicates non-response and `1` indicates response.\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "671b7881-6ab4-40c6-828e-e3e1934de37e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|##########| 1/1 [00:09<00:00,  9.08s/it]\n"]}], "source": ["df_tpm = pd.read_csv('./data/compass_gide_tpm.tsv', sep='\\t', index_col=0)\n", "#download a compass model from https://www.immuno-compass.com/download/model/LOCO/pft_leave_Gide.pt \n", "model = loadcompass('./model/pft_leave_Gide.pt', map_location = 'cpu')\n", "# Use map_location = 'cpu' if you dont have a GPU card\n", "_, df_pred = model.predict(df_tpm, batch_size=128)"]}, {"cell_type": "code", "execution_count": 4, "id": "2cb30921-f1ab-45d7-9ed2-43383435d88e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Index</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1_ipiPD1_PRE</th>\n", "      <td>0.429565</td>\n", "      <td>0.570435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2_ipiPD1_PRE</th>\n", "      <td>0.999955</td>\n", "      <td>0.000045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6_ipiPD1_PRE</th>\n", "      <td>0.224929</td>\n", "      <td>0.775071</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7_ipiPD1_PRE</th>\n", "      <td>0.004783</td>\n", "      <td>0.995217</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8_ipiPD1_PRE</th>\n", "      <td>0.841231</td>\n", "      <td>0.158769</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     0         1\n", "Index                           \n", "1_ipiPD1_PRE  0.429565  0.570435\n", "2_ipiPD1_PRE  0.999955  0.000045\n", "6_ipiPD1_PRE  0.224929  0.775071\n", "7_ipiPD1_PRE  0.004783  0.995217\n", "8_ipiPD1_PRE  0.841231  0.158769"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_pred.head()"]}, {"cell_type": "markdown", "id": "4672ff15-2c35-42a1-8b54-f157bc58d786", "metadata": {}, "source": ["\n", "## 3. Extracting Features with a Compass Model\n", "\n", "Both pre-trained (PT) and fine-tuned (FT) Compass models can function as feature extractors. The extracted gene-level, geneset-level, or cell type/pathway-level features can be used to build a logistic regression model for response prediction or a Cox regression model for survival prediction.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "2c0ba906-b8c0-40c9-9658-36b6d205e580", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|##########| 1/1 [00:00<00:00,  1.19it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CANCER</th>\n", "      <th>Bcell_general</th>\n", "      <th>Memory_Bcell</th>\n", "      <th>Na<PERSON>_Bcell</th>\n", "      <th>Plasma_cell</th>\n", "      <th>CD4_Tcell</th>\n", "      <th>CD8_Tcell</th>\n", "      <th>Memory_Tcell</th>\n", "      <th>Na<PERSON>_Tcell</th>\n", "      <th>Tcell_general</th>\n", "      <th>...</th>\n", "      <th>Pancreatic</th>\n", "      <th>Pneumocyte</th>\n", "      <th>Apoptosis_pathway</th>\n", "      <th>IFNg_pathway</th>\n", "      <th>TGFb_pathway</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Cell_proliferation</th>\n", "      <th>TLS</th>\n", "      <th>Genome_integrity</th>\n", "      <th>Reference</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1_ipiPD1_PRE</th>\n", "      <td>-0.757586</td>\n", "      <td>-0.262867</td>\n", "      <td>0.129370</td>\n", "      <td>-0.815162</td>\n", "      <td>0.278603</td>\n", "      <td>-0.169692</td>\n", "      <td>-0.026435</td>\n", "      <td>0.039869</td>\n", "      <td>-0.401233</td>\n", "      <td>-0.215705</td>\n", "      <td>...</td>\n", "      <td>0.099943</td>\n", "      <td>-0.063546</td>\n", "      <td>0.120870</td>\n", "      <td>-0.268174</td>\n", "      <td>-0.081785</td>\n", "      <td>-0.072616</td>\n", "      <td>-0.136177</td>\n", "      <td>-0.176708</td>\n", "      <td>0.152991</td>\n", "      <td>0.453108</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2_ipiPD1_PRE</th>\n", "      <td>-0.757746</td>\n", "      <td>-0.353675</td>\n", "      <td>0.393750</td>\n", "      <td>-0.811435</td>\n", "      <td>0.074256</td>\n", "      <td>-0.185324</td>\n", "      <td>-0.012123</td>\n", "      <td>0.077559</td>\n", "      <td>0.536754</td>\n", "      <td>-0.107892</td>\n", "      <td>...</td>\n", "      <td>0.080122</td>\n", "      <td>0.034633</td>\n", "      <td>0.113858</td>\n", "      <td>-0.265196</td>\n", "      <td>0.024003</td>\n", "      <td>-0.092963</td>\n", "      <td>-0.140686</td>\n", "      <td>-0.014389</td>\n", "      <td>0.203304</td>\n", "      <td>0.457289</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6_ipiPD1_PRE</th>\n", "      <td>-0.757299</td>\n", "      <td>-0.372067</td>\n", "      <td>0.161556</td>\n", "      <td>-0.798404</td>\n", "      <td>0.103220</td>\n", "      <td>-0.161113</td>\n", "      <td>0.021997</td>\n", "      <td>0.145440</td>\n", "      <td>0.043221</td>\n", "      <td>-0.112898</td>\n", "      <td>...</td>\n", "      <td>0.070483</td>\n", "      <td>-0.081517</td>\n", "      <td>0.147193</td>\n", "      <td>-0.191045</td>\n", "      <td>-0.072711</td>\n", "      <td>-0.065004</td>\n", "      <td>-0.167961</td>\n", "      <td>-0.164095</td>\n", "      <td>0.135996</td>\n", "      <td>0.448622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7_ipiPD1_PRE</th>\n", "      <td>-0.757101</td>\n", "      <td>-0.242434</td>\n", "      <td>0.178155</td>\n", "      <td>-0.803801</td>\n", "      <td>0.206927</td>\n", "      <td>-0.183297</td>\n", "      <td>0.033455</td>\n", "      <td>0.059158</td>\n", "      <td>-0.426837</td>\n", "      <td>-0.106407</td>\n", "      <td>...</td>\n", "      <td>0.026826</td>\n", "      <td>-0.105330</td>\n", "      <td>0.239903</td>\n", "      <td>-0.129926</td>\n", "      <td>-0.094376</td>\n", "      <td>-0.052420</td>\n", "      <td>-0.149514</td>\n", "      <td>-0.147732</td>\n", "      <td>0.146455</td>\n", "      <td>0.431817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8_ipiPD1_PRE</th>\n", "      <td>-0.758030</td>\n", "      <td>-0.396814</td>\n", "      <td>0.197401</td>\n", "      <td>-0.809641</td>\n", "      <td>0.081464</td>\n", "      <td>-0.181386</td>\n", "      <td>0.056288</td>\n", "      <td>0.205693</td>\n", "      <td>-0.000701</td>\n", "      <td>-0.137546</td>\n", "      <td>...</td>\n", "      <td>0.151547</td>\n", "      <td>-0.056724</td>\n", "      <td>0.052763</td>\n", "      <td>-0.204880</td>\n", "      <td>-0.098621</td>\n", "      <td>-0.074825</td>\n", "      <td>-0.063258</td>\n", "      <td>-0.099110</td>\n", "      <td>0.141699</td>\n", "      <td>0.468105</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 44 columns</p>\n", "</div>"], "text/plain": ["                CANCER  Bcell_general  Memory_Bcell  Naive_Bcell  Plasma_cell  \\\n", "Index                                                                           \n", "1_ipiPD1_PRE -0.757586      -0.262867      0.129370    -0.815162     0.278603   \n", "2_ipiPD1_PRE -0.757746      -0.353675      0.393750    -0.811435     0.074256   \n", "6_ipiPD1_PRE -0.757299      -0.372067      0.161556    -0.798404     0.103220   \n", "7_ipiPD1_PRE -0.757101      -0.242434      0.178155    -0.803801     0.206927   \n", "8_ipiPD1_PRE -0.758030      -0.396814      0.197401    -0.809641     0.081464   \n", "\n", "              CD4_Tcell  CD8_Tcell  Memory_Tcell  Naive_Tcell  Tcell_general  \\\n", "Index                                                                          \n", "1_ipiPD1_PRE  -0.169692  -0.026435      0.039869    -0.401233      -0.215705   \n", "2_ipiPD1_PRE  -0.185324  -0.012123      0.077559     0.536754      -0.107892   \n", "6_ipiPD1_PRE  -0.161113   0.021997      0.145440     0.043221      -0.112898   \n", "7_ipiPD1_PRE  -0.183297   0.033455      0.059158    -0.426837      -0.106407   \n", "8_ipiPD1_PRE  -0.181386   0.056288      0.205693    -0.000701      -0.137546   \n", "\n", "              ...  Pancreatic  Pneumocyte  Apoptosis_pathway  IFNg_pathway  \\\n", "Index         ...                                                            \n", "1_ipiPD1_PRE  ...    0.099943   -0.063546           0.120870     -0.268174   \n", "2_ipiPD1_PRE  ...    0.080122    0.034633           0.113858     -0.265196   \n", "6_ipiPD1_PRE  ...    0.070483   -0.081517           0.147193     -0.191045   \n", "7_ipiPD1_PRE  ...    0.026826   -0.105330           0.239903     -0.129926   \n", "8_ipiPD1_PRE  ...    0.151547   -0.056724           0.052763     -0.204880   \n", "\n", "              TGFb_pathway  Cytokine  Cell_proliferation       TLS  \\\n", "Index                                                                \n", "1_ipiPD1_PRE     -0.081785 -0.072616           -0.136177 -0.176708   \n", "2_ipiPD1_PRE      0.024003 -0.092963           -0.140686 -0.014389   \n", "6_ipiPD1_PRE     -0.072711 -0.065004           -0.167961 -0.164095   \n", "7_ipiPD1_PRE     -0.094376 -0.052420           -0.149514 -0.147732   \n", "8_ipiPD1_PRE     -0.098621 -0.074825           -0.063258 -0.099110   \n", "\n", "              Genome_integrity  Reference  \n", "Index                                      \n", "1_ipiPD1_PRE          0.152991   0.453108  \n", "2_ipiPD1_PRE          0.203304   0.457289  \n", "6_ipiPD1_PRE          0.135996   0.448622  \n", "7_ipiPD1_PRE          0.146455   0.431817  \n", "8_ipiPD1_PRE          0.141699   0.468105  \n", "\n", "[5 rows x 44 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Use any Compass model of your choice\n", "model = loadcompass('./model/pretrainer.pt') \n", "#downloaded from https://www.immuno-compass.com/download/model/pretrainer.pt \n", "dfgn, dfgs, dfct = model.extract(df_tpm, batch_size=128, with_gene_level=True)\n", "dfct.head()"]}, {"cell_type": "markdown", "id": "fba8c75b-7a9f-4838-b4be-e51b6f056b7d", "metadata": {}, "source": ["The outputs `dfgn`, `dfgs`, and `dfct` correspond to gene-level (15,672), geneset-level (133), and concept-level (44) features, respectively. The extracted features are scalar scores. If you need vector features (dim=32), use the following method:"]}, {"cell_type": "code", "execution_count": 6, "id": "c5229503-ac48-4843-8658-b6f9e49b1001", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|##########| 1/1 [00:00<00:00,  2.23it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>channel_0</th>\n", "      <th>channel_1</th>\n", "      <th>channel_2</th>\n", "      <th>channel_3</th>\n", "      <th>channel_4</th>\n", "      <th>channel_5</th>\n", "      <th>channel_6</th>\n", "      <th>channel_7</th>\n", "      <th>channel_8</th>\n", "      <th>channel_9</th>\n", "      <th>...</th>\n", "      <th>channel_22</th>\n", "      <th>channel_23</th>\n", "      <th>channel_24</th>\n", "      <th>channel_25</th>\n", "      <th>channel_26</th>\n", "      <th>channel_27</th>\n", "      <th>channel_28</th>\n", "      <th>channel_29</th>\n", "      <th>channel_30</th>\n", "      <th>channel_31</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1_ipiPD1_PRE$$Bcell_general</th>\n", "      <td>-0.034739</td>\n", "      <td>-0.332635</td>\n", "      <td>-0.642141</td>\n", "      <td>0.561939</td>\n", "      <td>0.020290</td>\n", "      <td>0.367835</td>\n", "      <td>0.518909</td>\n", "      <td>0.365392</td>\n", "      <td>0.175236</td>\n", "      <td>-0.415692</td>\n", "      <td>...</td>\n", "      <td>-0.021645</td>\n", "      <td>-0.236544</td>\n", "      <td>-0.747826</td>\n", "      <td>0.391741</td>\n", "      <td>-1.088722</td>\n", "      <td>0.756434</td>\n", "      <td>0.211727</td>\n", "      <td>-0.244743</td>\n", "      <td>0.848020</td>\n", "      <td>0.388430</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1_ipiPD1_PRE$$Memory_Bcell</th>\n", "      <td>-0.022590</td>\n", "      <td>-0.397354</td>\n", "      <td>-0.868363</td>\n", "      <td>1.010871</td>\n", "      <td>-0.118244</td>\n", "      <td>0.678905</td>\n", "      <td>0.769296</td>\n", "      <td>0.625515</td>\n", "      <td>0.419354</td>\n", "      <td>-0.524081</td>\n", "      <td>...</td>\n", "      <td>0.130836</td>\n", "      <td>-0.081616</td>\n", "      <td>-0.792151</td>\n", "      <td>0.098789</td>\n", "      <td>-0.996874</td>\n", "      <td>0.424624</td>\n", "      <td>0.028835</td>\n", "      <td>0.097752</td>\n", "      <td>1.040196</td>\n", "      <td>0.372795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1_ipiPD1_PRE$$Naive_Bcell</th>\n", "      <td>0.107904</td>\n", "      <td>-0.501725</td>\n", "      <td>-0.788940</td>\n", "      <td>0.905886</td>\n", "      <td>-0.090654</td>\n", "      <td>0.603379</td>\n", "      <td>0.865653</td>\n", "      <td>0.840186</td>\n", "      <td>0.485864</td>\n", "      <td>-0.464720</td>\n", "      <td>...</td>\n", "      <td>0.155620</td>\n", "      <td>0.130635</td>\n", "      <td>-0.838648</td>\n", "      <td>0.262417</td>\n", "      <td>-0.950026</td>\n", "      <td>0.437575</td>\n", "      <td>-0.043248</td>\n", "      <td>-0.035564</td>\n", "      <td>0.939187</td>\n", "      <td>0.296764</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1_ipiPD1_PRE$$Plasma_cell</th>\n", "      <td>0.218896</td>\n", "      <td>-0.407893</td>\n", "      <td>-0.895497</td>\n", "      <td>0.977370</td>\n", "      <td>-0.078406</td>\n", "      <td>0.657787</td>\n", "      <td>0.624934</td>\n", "      <td>0.675836</td>\n", "      <td>0.520595</td>\n", "      <td>-0.495843</td>\n", "      <td>...</td>\n", "      <td>0.155416</td>\n", "      <td>-0.208327</td>\n", "      <td>-0.602620</td>\n", "      <td>0.115889</td>\n", "      <td>-1.072236</td>\n", "      <td>0.395798</td>\n", "      <td>-0.030286</td>\n", "      <td>-0.012423</td>\n", "      <td>1.086152</td>\n", "      <td>0.323278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1_ipiPD1_PRE$$CD4_Tcell</th>\n", "      <td>0.093407</td>\n", "      <td>-0.404865</td>\n", "      <td>-0.768114</td>\n", "      <td>0.946871</td>\n", "      <td>0.021003</td>\n", "      <td>0.491076</td>\n", "      <td>0.790252</td>\n", "      <td>0.711688</td>\n", "      <td>0.260992</td>\n", "      <td>-0.424142</td>\n", "      <td>...</td>\n", "      <td>0.110076</td>\n", "      <td>-0.188954</td>\n", "      <td>-0.752739</td>\n", "      <td>0.272066</td>\n", "      <td>-0.948871</td>\n", "      <td>0.518185</td>\n", "      <td>0.120522</td>\n", "      <td>-0.022940</td>\n", "      <td>0.828513</td>\n", "      <td>0.329999</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 32 columns</p>\n", "</div>"], "text/plain": ["                             channel_0  channel_1  channel_2  channel_3  \\\n", "1_ipiPD1_PRE$$Bcell_general  -0.034739  -0.332635  -0.642141   0.561939   \n", "1_ipiPD1_PRE$$Memory_Bcell   -0.022590  -0.397354  -0.868363   1.010871   \n", "1_ipiPD1_PRE$$Naive_Bcell     0.107904  -0.501725  -0.788940   0.905886   \n", "1_ipiPD1_PRE$$Plasma_cell     0.218896  -0.407893  -0.895497   0.977370   \n", "1_ipiPD1_PRE$$CD4_Tcell       0.093407  -0.404865  -0.768114   0.946871   \n", "\n", "                             channel_4  channel_5  channel_6  channel_7  \\\n", "1_ipiPD1_PRE$$Bcell_general   0.020290   0.367835   0.518909   0.365392   \n", "1_ipiPD1_PRE$$Memory_Bcell   -0.118244   0.678905   0.769296   0.625515   \n", "1_ipiPD1_PRE$$Naive_Bcell    -0.090654   0.603379   0.865653   0.840186   \n", "1_ipiPD1_PRE$$Plasma_cell    -0.078406   0.657787   0.624934   0.675836   \n", "1_ipiPD1_PRE$$CD4_Tcell       0.021003   0.491076   0.790252   0.711688   \n", "\n", "                             channel_8  channel_9  ...  channel_22  \\\n", "1_ipiPD1_PRE$$Bcell_general   0.175236  -0.415692  ...   -0.021645   \n", "1_ipiPD1_PRE$$Memory_Bcell    0.419354  -0.524081  ...    0.130836   \n", "1_ipiPD1_PRE$$Naive_Bcell     0.485864  -0.464720  ...    0.155620   \n", "1_ipiPD1_PRE$$Plasma_cell     0.520595  -0.495843  ...    0.155416   \n", "1_ipiPD1_PRE$$CD4_Tcell       0.260992  -0.424142  ...    0.110076   \n", "\n", "                             channel_23  channel_24  channel_25  channel_26  \\\n", "1_ipiPD1_PRE$$Bcell_general   -0.236544   -0.747826    0.391741   -1.088722   \n", "1_ipiPD1_PRE$$Memory_Bcell    -0.081616   -0.792151    0.098789   -0.996874   \n", "1_ipiPD1_PRE$$Naive_Bcell      0.130635   -0.838648    0.262417   -0.950026   \n", "1_ipiPD1_PRE$$Plasma_cell     -0.208327   -0.602620    0.115889   -1.072236   \n", "1_ipiPD1_PRE$$CD4_Tcell       -0.188954   -0.752739    0.272066   -0.948871   \n", "\n", "                             channel_27  channel_28  channel_29  channel_30  \\\n", "1_ipiPD1_PRE$$Bcell_general    0.756434    0.211727   -0.244743    0.848020   \n", "1_ipiPD1_PRE$$Memory_Bcell     0.424624    0.028835    0.097752    1.040196   \n", "1_ipiPD1_PRE$$Naive_Bcell      0.437575   -0.043248   -0.035564    0.939187   \n", "1_ipiPD1_PRE$$Plasma_cell      0.395798   -0.030286   -0.012423    1.086152   \n", "1_ipiPD1_PRE$$CD4_Tcell        0.518185    0.120522   -0.022940    0.828513   \n", "\n", "                             channel_31  \n", "1_ipiPD1_PRE$$Bcell_general    0.388430  \n", "1_ipiPD1_PRE$$Memory_Bcell     0.372795  \n", "1_ipiPD1_PRE$$Naive_Bcell      0.296764  \n", "1_ipiPD1_PRE$$Plasma_cell      0.323278  \n", "1_ipiPD1_PRE$$CD4_Tcell        0.329999  \n", "\n", "[5 rows x 32 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dfgs, dfct = model.project(df_tpm, batch_size=128)\n", "dfct.head()"]}, {"cell_type": "markdown", "id": "692427c0-95e3-4f5d-8270-4e5464a83fc9", "metadata": {}, "source": ["\n", "## 4. <PERSON>-Tu<PERSON> Compass on Your Own Data\n", "\n", "If you have in-house data and would like to fine-tune a Compass model, you can use any Compass model for fine-tuning. You can either load the pre-trained Compass model or a publicly available fine-tuned Compass model.\n", "\n", "**Important Note:** If you choose a fine-tuned model for further fine-tune (multi-stage FT), ensure that the `load_decoder` parameter in `ft_args` is set to `True`:\n", "```python\n", "ft_args = {'load_decoder': True}\n", "```\n"]}, {"cell_type": "code", "execution_count": 7, "id": "586f39db-38b5-45a4-8e3e-63bcbdf008e0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 34%|###4      | 34/100 [00:46<01:30,  1.38s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Stopping early at epoch 35. Meet minimal requirements by: f1=0.93,mcc=0.85,prc=0.91, roc=0.92\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saving the model to ./model/my_finetuner.pt\n"]}], "source": ["### Example Fine-Tuning Process\n", "model = loadcompass('./model/finetuner_pft_all.pt')  \n", "ft_args = {'mode': 'PFT', 'lr': 1e-3, 'batch_size': 16, 'max_epochs': 100, 'verbose':0, 'load_decoder': True}\n", "finetuner = FineTuner(model, **ft_args)\n", "\n", "# Load the true labels\n", "df_cln = pd.read_csv('./data/compass_gide_clinical.tsv', sep='\\t', index_col=0)\n", "dfy = pd.get_dummies(df_cln.response_label)\n", "\n", "# Fine-tune the model\n", "finetuner.tune(df_tpm, dfy)\n", "finetuner.save('./model/my_finetuner.pt')"]}, {"cell_type": "markdown", "id": "52b63a2f-4525-4f36-b189-59937ebaf072", "metadata": {}, "source": ["This process fine-tunes the Compass model on your data and saves the updated model for future use."]}, {"cell_type": "markdown", "id": "8148abdc-3c13-49d5-b151-e5bfcd07cda6", "metadata": {}, "source": ["\n", "\n", "## 5. Pre-training Co<PERSON>ass from Scracth\n"]}, {"cell_type": "code", "execution_count": 8, "id": "a6658e1f-96b4-4102-9614-9987557239fb", "metadata": {}, "outputs": [], "source": ["# Load the example dataset for pretraining\n", "# We provide sample datasets contain gene expression data for training and testing\n", "# Ensure the data is preprocessed appropriately before use\n", "tcga_train_sample = pd.read_csv('./data/tcga_example_train.tsv', sep='\\t', index_col=0)\n", "tcga_test_sample = pd.read_csv('./data/tcga_example_test.tsv', sep='\\t', index_col=0)"]}, {"cell_type": "code", "execution_count": 9, "id": "4aea63c4-b66e-4a08-bb72-fa05e12039a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch: 1/20 - Train Loss: 0.8674 - Test Loss: 0.7814\n", "Epoch: 2/20 - Train Loss: 0.6865 - Test Loss: 0.4156\n", "Epoch: 3/20 - Train Loss: 0.4821 - Test Loss: 0.3902\n", "Epoch: 4/20 - Train Loss: 0.4101 - Test Loss: 0.3735\n", "Epoch: 5/20 - Train Loss: 0.3411 - Test Loss: 0.2704\n", "Epoch: 6/20 - Train Loss: 0.3355 - Test Loss: 0.1565\n", "Epoch: 7/20 - Train Loss: 0.2669 - Test Loss: 0.1779\n", "Epoch: 8/20 - Train Loss: 0.2230 - Test Loss: 0.2203\n", "Epoch: 9/20 - Train Loss: 0.1856 - Test Loss: 0.1900\n", "Epoch: 10/20 - Train Loss: 0.1849 - Test Loss: 0.1496\n", "Epoch: 11/20 - Train Loss: 0.1910 - Test Loss: 0.1536\n", "Epoch: 12/20 - Train Loss: 0.1606 - Test Loss: 0.2318\n", "Epoch: 13/20 - Train Loss: 0.1566 - Test Loss: 0.1369\n", "Epoch: 14/20 - Train Loss: 0.1749 - Test Loss: 0.0824\n", "Epoch: 15/20 - Train Loss: 0.1456 - Test Loss: 0.2460\n", "Epoch: 16/20 - Train Loss: 0.1415 - Test Loss: 0.1461\n", "Epoch: 17/20 - Train Loss: 0.1379 - Test Loss: 0.1153\n", "Epoch: 18/20 - Train Loss: 0.1359 - Test Loss: 0.1563\n", "Epoch: 19/20 - Train Loss: 0.1323 - Test Loss: 0.2430\n", "Epoch: 20/20 - Train Loss: 0.1481 - Test Loss: 0.1498\n", "Saving final model...\n", "\n", "Best validation loss: 0.08239942789077759\n", "\n", "Saving best model on epoch: 14\n", "\n", "Saving the model to ./model/my_pretrainer.pt\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 700x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Define pretraining hyperparameters\n", "pt_args = {'lr': 1e-3, 'batch_size': 96, 'epochs': 20, 'seed':42}\n", "pretrainer = PreTrainer(**pt_args)\n", "\n", "# Train the model using the provided training and test datasets\n", "# - dfcx_train: Training dataset\n", "# - dfcx_test: Validation dataset to monitor performance\n", "pretrainer.train(dfcx_train=tcga_train_sample,\n", "                 dfcx_test=tcga_test_sample)\n", "\n", "# Save the trained pretrainer model for future use\n", "pretrainer.save('./model/my_pretrainer.pt')"]}, {"cell_type": "markdown", "id": "da6b56ba-0173-4a36-9c0f-c4f845ae27a9", "metadata": {}, "source": ["## 6. Baseline Methods Usage Examples"]}, {"cell_type": "code", "execution_count": 10, "id": "07ffd6f6-f3b9-45b3-901c-4a63c798c4ae", "metadata": {}, "outputs": [], "source": ["# import the baseline scores\n", "from baseline.immnue_score import immnue_score_methods"]}, {"cell_type": "code", "execution_count": 11, "id": "6b335829-0092-4151-8163-0ca081f90b3a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'PD1': baseline.immnue_score.PD1.PD1,\n", " 'PDL1': baseline.immnue_score.PDL1.PDL1,\n", " 'CTLA4': baseline.immnue_score.CTLA4.CTLA4,\n", " 'CD8': baseline.immnue_score.CD8.CD8,\n", " 'GeneBio': baseline.immnue_score.GeneBio.GeneBio,\n", " 'NetBio': baseline.immnue_score.Kong_NetBio.Kong_NetBio,\n", " 'MIAS': baseline.immnue_score.Wu_MIAS.Wu_MIAS,\n", " 'GEP': baseline.immnue_score.<PERSON><PERSON><PERSON>_GEP.<PERSON><PERSON><PERSON>_GEP,\n", " 'IMPRES': baseline.immnue_score.Auslander_IMPRES.Auslander_IMPRES,\n", " 'TIDE': baseline.immnue_score.Jiang_TIDE.Jiang_TIDE,\n", " 'NRS': baseline.immnue_score.Huang_NRS.Huang_NRS,\n", " 'IFNG': baseline.immnue_score.Ayers_IFNG.Ayers_IFNG,\n", " 'CIS': baseline.immnue_score.Davoli_CIS.Davoli_CIS,\n", " 'IS': baseline.immnue_score.Roh_IS.Roh_IS,\n", " 'Teff': baseline.immnue_score.<PERSON><PERSON><PERSON>er_Teff.<PERSON><PERSON><PERSON><PERSON>_Te<PERSON>,\n", " 'PGM': baseline.immnue_score.Freeman_PGM.Freeman_PGM,\n", " 'CKS': baseline.immnue_score.Messina_CKS.Messina_CKS,\n", " 'CAF': baseline.immnue_score.Nurmik_CAFs.Nurmik_CAFs,\n", " 'CTL': baseline.immnue_score.Jiang_CTLs.Jiang_CTLs,\n", " 'TAM': baseline.immnue_score.Jiang_TAMs.Jiang_TAMs,\n", " 'Texh': baseline.immnue_score.Jiang_Texh.Jiang_<PERSON>,\n", " 'ICA': baseline.immnue_score.Rooney_ICA.Rooney_ICA}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["immnue_score_methods"]}, {"cell_type": "code", "execution_count": 12, "id": "3ced9754-e2b4-40fb-b5bd-0eabe482e3fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[WARN] The majority(>80%) of genes with positive expression in your inputted data. Please Normalize your data\n", "[WARN] Start normalizing the input expression profile by: 1. Do the log2(x+1) transformation. 2. Subtract the average across your samples.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PDCD1</th>\n", "      <th>CD274</th>\n", "      <th>CTLA4</th>\n", "      <th>NAG_CD8</th>\n", "      <th>CD8B</th>\n", "      <th>CD8A</th>\n", "      <th>CD28</th>\n", "      <th>TRIM25</th>\n", "      <th>UBC</th>\n", "      <th>KIF2C</th>\n", "      <th>...</th>\n", "      <th>NAG_Roh_IS</th>\n", "      <th><PERSON><PERSON>_Fe<PERSON>bacher_Teff</th>\n", "      <th>TBX3</th>\n", "      <th>MAP4K1</th>\n", "      <th>PCA_Messina_CKS</th>\n", "      <th>NAG_Nurmik_CAFs</th>\n", "      <th>NAG_Jiang_CTLs</th>\n", "      <th>NAG_Jiang_TAMs</th>\n", "      <th>NAG_Jiang_Texh</th>\n", "      <th>NAG_Rooney_ICA</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1_ipiPD1_PRE</th>\n", "      <td>0.263034</td>\n", "      <td>0.443607</td>\n", "      <td>0.584963</td>\n", "      <td>0.075813</td>\n", "      <td>0.000000</td>\n", "      <td>0.333424</td>\n", "      <td>0.678072</td>\n", "      <td>4.095924</td>\n", "      <td>8.877867</td>\n", "      <td>2.472488</td>\n", "      <td>...</td>\n", "      <td>0.529500</td>\n", "      <td>0.255713</td>\n", "      <td>2.100978</td>\n", "      <td>2.220330</td>\n", "      <td>-5.076745</td>\n", "      <td>0.685883</td>\n", "      <td>0.297893</td>\n", "      <td>0.688596</td>\n", "      <td>0.740698</td>\n", "      <td>0.173749</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2_ipiPD1_PRE</th>\n", "      <td>0.124328</td>\n", "      <td>0.659925</td>\n", "      <td>1.042644</td>\n", "      <td>0.459469</td>\n", "      <td>0.250962</td>\n", "      <td>1.769772</td>\n", "      <td>1.761285</td>\n", "      <td>3.675816</td>\n", "      <td>10.520422</td>\n", "      <td>3.733354</td>\n", "      <td>...</td>\n", "      <td>0.850548</td>\n", "      <td>0.292490</td>\n", "      <td>3.990955</td>\n", "      <td>3.568032</td>\n", "      <td>-4.282737</td>\n", "      <td>1.008657</td>\n", "      <td>0.423985</td>\n", "      <td>0.898566</td>\n", "      <td>1.026243</td>\n", "      <td>0.590691</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6_ipiPD1_PRE</th>\n", "      <td>0.226509</td>\n", "      <td>0.263034</td>\n", "      <td>0.378512</td>\n", "      <td>0.370453</td>\n", "      <td>0.201634</td>\n", "      <td>1.427606</td>\n", "      <td>1.304511</td>\n", "      <td>2.916477</td>\n", "      <td>8.121793</td>\n", "      <td>1.214125</td>\n", "      <td>...</td>\n", "      <td>0.707502</td>\n", "      <td>0.490161</td>\n", "      <td>0.925999</td>\n", "      <td>2.508429</td>\n", "      <td>-2.801631</td>\n", "      <td>0.754903</td>\n", "      <td>0.527356</td>\n", "      <td>0.540569</td>\n", "      <td>0.660849</td>\n", "      <td>0.459451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7_ipiPD1_PRE</th>\n", "      <td>0.286881</td>\n", "      <td>0.238787</td>\n", "      <td>0.545968</td>\n", "      <td>0.645404</td>\n", "      <td>0.389567</td>\n", "      <td>2.448901</td>\n", "      <td>0.594549</td>\n", "      <td>2.056584</td>\n", "      <td>7.470211</td>\n", "      <td>1.395063</td>\n", "      <td>...</td>\n", "      <td>0.722526</td>\n", "      <td>0.695194</td>\n", "      <td>1.627607</td>\n", "      <td>1.761285</td>\n", "      <td>-1.213075</td>\n", "      <td>0.497768</td>\n", "      <td>0.527554</td>\n", "      <td>0.472271</td>\n", "      <td>0.488581</td>\n", "      <td>0.396062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8_ipiPD1_PRE</th>\n", "      <td>0.189034</td>\n", "      <td>0.584963</td>\n", "      <td>0.978196</td>\n", "      <td>0.535612</td>\n", "      <td>0.344828</td>\n", "      <td>2.010780</td>\n", "      <td>3.321928</td>\n", "      <td>4.436961</td>\n", "      <td>8.370426</td>\n", "      <td>3.837943</td>\n", "      <td>...</td>\n", "      <td>0.709227</td>\n", "      <td>0.736650</td>\n", "      <td>1.028569</td>\n", "      <td>2.680324</td>\n", "      <td>-2.420211</td>\n", "      <td>1.002617</td>\n", "      <td>0.582450</td>\n", "      <td>0.794207</td>\n", "      <td>0.931874</td>\n", "      <td>0.254326</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 222 columns</p>\n", "</div>"], "text/plain": ["                 PDCD1     CD274     CTLA4   NAG_CD8      CD8B      CD8A  \\\n", "1_ipiPD1_PRE  0.263034  0.443607  0.584963  0.075813  0.000000  0.333424   \n", "2_ipiPD1_PRE  0.124328  0.659925  1.042644  0.459469  0.250962  1.769772   \n", "6_ipiPD1_PRE  0.226509  0.263034  0.378512  0.370453  0.201634  1.427606   \n", "7_ipiPD1_PRE  0.286881  0.238787  0.545968  0.645404  0.389567  2.448901   \n", "8_ipiPD1_PRE  0.189034  0.584963  0.978196  0.535612  0.344828  2.010780   \n", "\n", "                  CD28    TRIM25        UBC     KIF2C  ...  NAG_Roh_IS  \\\n", "1_ipiPD1_PRE  0.678072  4.095924   8.877867  2.472488  ...    0.529500   \n", "2_ipiPD1_PRE  1.761285  3.675816  10.520422  3.733354  ...    0.850548   \n", "6_ipiPD1_PRE  1.304511  2.916477   8.121793  1.214125  ...    0.707502   \n", "7_ipiPD1_PRE  0.594549  2.056584   7.470211  1.395063  ...    0.722526   \n", "8_ipiPD1_PRE  3.321928  4.436961   8.370426  3.837943  ...    0.709227   \n", "\n", "              <PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Teff      TBX3    MAP4K1  PCA_Messina_CKS  \\\n", "1_ipiPD1_PRE               0.255713  2.100978  2.220330        -5.076745   \n", "2_ipiPD1_PRE               0.292490  3.990955  3.568032        -4.282737   \n", "6_ipiPD1_PRE               0.490161  0.925999  2.508429        -2.801631   \n", "7_ipiPD1_PRE               0.695194  1.627607  1.761285        -1.213075   \n", "8_ipiPD1_PRE               0.736650  1.028569  2.680324        -2.420211   \n", "\n", "              NAG_Nurmik_CAFs  NAG_Jiang_CTLs  NAG_Jiang_TAMs  NAG_Jiang_Texh  \\\n", "1_ipiPD1_PRE         0.685883        0.297893        0.688596        0.740698   \n", "2_ipiPD1_PRE         1.008657        0.423985        0.898566        1.026243   \n", "6_ipiPD1_PRE         0.754903        0.527356        0.540569        0.660849   \n", "7_ipiPD1_PRE         0.497768        0.527554        0.472271        0.488581   \n", "8_ipiPD1_PRE         1.002617        0.582450        0.794207        0.931874   \n", "\n", "              NAG_Rooney_ICA  \n", "1_ipiPD1_PRE        0.173749  \n", "2_ipiPD1_PRE        0.590691  \n", "6_ipiPD1_PRE        0.459451  \n", "7_ipiPD1_PRE        0.396062  \n", "8_ipiPD1_PRE        0.254326  \n", "\n", "[5 rows x 222 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# import the baseline scores\n", "from baseline.immnue_score import immnue_score_methods\n", "\n", "# Below is how to extract the features using the baseline methods.\n", "# These features can be used to develop logistic regression model for response prediction\n", "res = []\n", "for k, f in immnue_score_methods.items():\n", "    baseline = f(cancer_type='SKCM', drug_target='PD1')\n", "    s1 = baseline(df_tpm)\n", "    res.append(s1)\n", "\n", "pd.concat(res, axis=1).head()"]}, {"cell_type": "markdown", "id": "735ce6fa-57e4-463d-98ef-cd1bbea48948", "metadata": {}, "source": ["## 7. Additional Information\r\n", "\r\n", "This section provides detailed information to help you get started with the Compass project. We explain how to generate the necessary inputs from raw FASTQ data and introduce our online web server that supports both prediction and feature extraction using our pre-trained Compass models.\r\n", "\r\n", "### Generating Compass Inputs from Raw FASTQ Data\r\n", "\r\n", "Generating high-quality inputs is crucial for the optimal performance of the Compass models. Our comprehensive [Compass Data Pre-Processing Guide](https://www.immuno-compass.com/help/index.html) walks you through the entire workflow, ensuring that your raw FASTQ data is processed into a robust format ready for accurate predictions and feature extraction.\r\n", "\r\n", "### Online Web Server for Prediction and Feature Extraction\r\n", "\r\n", "To simplify the use of our models, we offer an online web server that enables you to interact directly with the Compass models without local installations. The web server provides two primary functionalities:\r\n", "\r\n", "- **Prediction:** Submit your processed data to generate model predictions using our [online prediction tool](https://www.immuno-compass.com/predict).\r\n", "- **Feature Extraction:** Extract key data attributes with our [feature extraction tool](https://www.immuno-compass.com/extract).\r\n", "\r\n", "These user-friendly services are designed to streamline your workflow and integrate Compass seamlessly into your analytical processes.\r\n", "\r\n", "### Contributing Your Own Compass Models\r\n", "\r\n", "We welcome contributions from the community. If you have developed a Compass model that can enhance our project, we encourage you to share it. By contributing your model, you help enrich the Compass ecosystem and promote collaborative innovation. For details on how to submit your model, please refer to our contribution guidelines. You can also [join our Slack channel](https://join.slack.com/t/immuno-compass/shared_invite/zt-2znjho738-YZOfLEGLNEH5eH_0W1TmQg) to discuss and collaborate with other users.\r\n", "\r\n", "### Citing Our Work\r\n", "\r\n", "If you use our resources, please cite our work as follows:\r\n", "\r\n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. *Cross-cancer AI modeling of immunotherapy response* [J]. medRxiv.\r\n", "\r\n", "---\r\n", "\r\n", "We hope this information helps you make the most of the Compass project. If you have any questions or need further assistance, please do not hesitate to contact our support [team](https://www.immuno-compass.com/contact/index.html).\r\n", "itate to contact our support team.\r\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}