# Compass Input Requirement¶

Please note that the input for Compass should be mRNA's TPM (Transcripts Per Million) expression values, not raw counts or other forms of mRNA expression values. TPM calculation is similar to FPKM but differs in the normalization process. In TPM, all transcripts are normalized for length first. Then, instead of using the total overall read count for size normalization, the sum of the length-normalized transcript values is used as a size indicator.

Please fell free to contact me if you have any questions on this.

## Data Processing Recommendation¶

If your data is in raw sequence format (FASTQ) or as raw counts, we recommend processing it using the following bioinformatics pipeline. This recommendation is based on the fact that our pretrained TCGA (The Cancer Genome Atlas) data was processed using this pipeline, and using the same pipeline for your input data may yield better results.

### 1. mRNA-Seq Alignment Workflow¶

The RNA-Seq Alignment Workflow follows these steps:

fastqc/multiqc  --&gt; fastp ---&gt; STAR2 align (two-pass method)

For more information, please refer to GDC mRNA expression pipeline.

#### Specific Process¶

1. Begin with quality control using fastqc and multiqc.
2. Proceed with data preprocessing using fastp to clean raw sequencing data and improve quality.
3. Finally, align the RNA-seq reads to a reference genome using STAR version 2.7.5c, which maps RNA-seq reads to the reference genome. While custom index files can be created, we use the reference genome files downloaded from GDC. The link for the specific reference genome file (star-2.7.5c\_GRCh38.d1.vd1\_gencode.v36.tgz) is available here.

### 2. Converting mRNA Raw Counts to TPM¶

If your data is in mRNA expression counts, you can convert the mRNA raw counts to TPM values using the following method.
This process involves normalization using gene lengths, so you will need to download the gene annotation file (v36).on.gtf.gz

#### Step 1: Download the human GENCODE annotation file (v36)¶

Download the GENCODE human annotation file (version 36) from the following link:

```
wget https://ftp.ebi.ac.uk/pub/databases/gencode/Gencode_human/release_36/gencode.v36.annotation.gtf.gz
mv gencode.v36.annotation.gtf.gz ./data
```

#### step2: using rnanorm tool to convert Count to TPM¶

```
#install rnanorm
pip install rnanorm
```

```
from rnanorm import FPKM, TPM, CPM, TMM 
gtf_path = "./gencode.v36.annotation.gtf"
tpm = TPM(gtf_path).set_output(transform="pandas")
df_tpm = tpm.fit_transform(df_counts)
```

#### step3: Now let's test on an example file¶

In [1]:

```
# import packages
import pandas as pd
from rnanorm import FPKM, TPM, CPM, TMM
```

In [2]:

```
# convert count to TPM based the gtf file
gtf_path = "https://www.immuno-compass.com/download/other/gencode.v36.annotation.gtf.gz"
tpm = TPM(gtf_path).set_output(transform="pandas")
```

In [3]:

```
# example of the raw counts
df_counts = pd.read_csv('https://www.immuno-compass.com/download/other/toy_raw_counts.csv', index_col=0)
df_counts.head()
```

Out[3]:

|            |   ENSG00000223972.5 |   ENSG00000227232.5 |   ENSG00000278267.1 |   ENSG00000243485.5 |   ENSG00000284332.1 |   ENSG00000237613.2 |   ENSG00000268020.3 |   ENSG00000240361.2 |   ENSG00000186092.6 |   ENSG00000238009.6 | ...   |   ENSG00000198886.2 |   ENSG00000210176.1 |   ENSG00000210184.1 |   ENSG00000210191.1 |   ENSG00000198786.2 |   ENSG00000198695.2 |   ENSG00000210194.1 |   ENSG00000198727.2 |   ENSG00000210195.2 |   ENSG00000210196.2 |
|------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|-------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|
| ERR2208944 |                   6 |                 201 |                   0 |                   0 |                   0 |                   1 |                   0 |                   0 |                   0 |                   0 | ...   |                1376 |                   0 |                   0 |                   0 |                 947 |                 178 |                   0 |                 582 |                   0 |                   4 |
| ERR2208928 |                   0 |                 222 |                   1 |                   0 |                   0 |                   0 |                   0 |                   0 |                   0 |                   0 | ...   |                2263 |                   0 |                   0 |                   0 |                2549 |                 459 |                   0 |                1486 |                   0 |                   0 |
| ERR2208949 |                   1 |                 487 |                   0 |                   0 |                   0 |                   3 |                   0 |                   0 |                   0 |                   0 | ...   |                2544 |                   1 |                   0 |                   0 |                1783 |                 377 |                   0 |                 745 |                   0 |                   4 |
| ERR2208900 |                  13 |                 569 |                   0 |                   0 |                   0 |                  14 |                   0 |                   0 |                   0 |                   4 | ...   |               13168 |                   4 |                   3 |                   1 |               10988 |                2702 |                   0 |                3746 |                   0 |                 101 |
| ERR2208922 |                   0 |                  29 |                   1 |                   0 |                   0 |                   0 |                   0 |                   0 |                   0 |                   0 | ...   |               14029 |                   2 |                   0 |                   0 |                5480 |                1302 |                   0 |                4160 |                   0 |                   6 |

5 rows × 60660 columns

In [4]:

```
# example of the TPM values
df_tpm = tpm.fit_transform(df_counts)
df_tpm.to_csv('./toy_tpm.csv')
df_tpm.head()
```

Out[4]:

|            |   ENSG00000223972.5 |   ENSG00000227232.5 |   ENSG00000278267.1 |   ENSG00000243485.5 |   ENSG00000284332.1 |   ENSG00000237613.2 |   ENSG00000268020.3 |   ENSG00000240361.2 |   ENSG00000186092.6 |   ENSG00000238009.6 | ...   |   ENSG00000198886.2 |   ENSG00000210176.1 |   ENSG00000210184.1 |   ENSG00000210191.1 |   ENSG00000198786.2 |   ENSG00000198695.2 |   ENSG00000210194.1 |   ENSG00000198727.2 |   ENSG00000210195.2 |   ENSG00000210196.2 |
|------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|-------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|
| ERR2208944 |            0.460737 |            19.8218  |             0       |                   0 |                   0 |            0.109294 |                   0 |                   0 |                   0 |            0        | ...   |             133.036 |             0       |             0       |            0        |             69.6295 |             45.1712 |                   0 |             67.9577 |                   0 |             7.83705 |
| ERR2208928 |            0        |            25.3828  |             2.27161 |                   0 |                   0 |            0        |                   0 |                   0 |                   0 |            0        | ...   |             253.675 |             0       |             0       |            0        |            217.297  |            135.05   |                   0 |            201.176  |                   0 |             0       |
| ERR2208949 |            0.069637 |            43.5524  |             0       |                   0 |                   0 |            0.297342 |                   0 |                   0 |                   0 |            0        | ...   |             223.052 |             1.75101 |             0       |            0        |            118.886  |             86.7602 |                   0 |             78.8877 |                   0 |             7.10705 |
| ERR2208900 |            0.172087 |             9.67298 |             0       |                   0 |                   0 |            0.263771 |                   0 |                   0 |                   0 |            0.024656 | ...   |             219.469 |             1.33142 |             1.16781 |            0.323478 |            139.272  |            118.203  |                   0 |             75.4025 |                   0 |            34.1127  |
| ERR2208922 |            0        |             2.36045 |             1.61713 |                   0 |                   0 |            0        |                   0 |                   0 |                   0 |            0        | ...   |            1119.52  |             3.18738 |             0       |            0        |            332.564  |            272.712  |                   0 |            400.922  |                   0 |             9.70275 |

5 rows × 60660 columns

### 3. Preparing the inputs for the Compass¶

The Inputs of Compass model including the cancer type information and TPM values, the genes are identified by gene name, and gene name can be mapped from a dictionary contains the gene Ensembl ID, Entrez gene ID, and gene name.

Please find the cancer code of your data from this table: TCGA Study Abbreviations:

| Study Abbreviation   | Study Name                                                       |
|----------------------|------------------------------------------------------------------|
| LAML                 | Acute Myeloid Leukemia                                           |
| ACC                  | Adrenocortical carcinoma                                         |
| BLCA                 | Bladder Urothelial Carcinoma                                     |
| LGG                  | Brain Lower Grade Glioma                                         |
| BRCA                 | Breast invasive carcinoma                                        |
| CESC                 | Cervical squamous cell carcinoma and endocervical adenocarcinoma |
| CHOL                 | Cholangiocarcinoma                                               |
| LCML                 | Chronic Myelogenous Leukemia                                     |
| COAD                 | Colon adenocarcinoma                                             |
| CNTL                 | Controls                                                         |
| ESCA                 | Esophageal carcinoma                                             |
| FPPP                 | FFPE Pilot Phase II                                              |
| GBM                  | Glioblastoma multiforme                                          |
| HNSC                 | Head and Neck squamous cell carcinoma                            |
| KICH                 | Kidney Chromophobe                                               |
| KIRC                 | Kidney renal clear cell carcinoma                                |
| KIRP                 | Kidney renal papillary cell carcinoma                            |
| LIHC                 | Liver hepatocellular carcinoma                                   |
| LUAD                 | Lung adenocarcinoma                                              |
| LUSC                 | Lung squamous cell carcinoma                                     |
| DLBC                 | Lymphoid Neoplasm Diffuse Large B-cell Lymphoma                  |
| MESO                 | Mesothelioma                                                     |
| MISC                 | Miscellaneous                                                    |
| OV                   | Ovarian serous cystadenocarcinoma                                |
| PAAD                 | Pancreatic adenocarcinoma                                        |
| PCPG                 | Pheochromocytoma and Paraganglioma                               |
| PRAD                 | Prostate adenocarcinoma                                          |
| READ                 | Rectum adenocarcinoma                                            |
| SARC                 | Sarcoma                                                          |
| SKCM                 | Skin Cutaneous Melanoma                                          |
| STAD                 | Stomach adenocarcinoma                                           |
| TGCT                 | Testicular Germ Cell Tumors                                      |
| THYM                 | Thymoma                                                          |
| THCA                 | Thyroid carcinoma                                                |
| UCS                  | Uterine Carcinosarcoma                                           |
| UCEC                 | Uterine Corpus Endometrial Carcinoma                             |
| UVM                  | Uveal Melanoma                                                   |

#### Step1. Add the cancer type information¶

Suppose your data are all from Melonoma, here is an example to generate the Compass's cancer type

In [5]:

```
df_cancer_type = pd.DataFrame([], index = df_counts.index)
df_cancer_type['cancer_type'] = 'SKCM'
df_cancer_type.head()
```

Out[5]:

|            | cancer_type   |
|------------|---------------|
| ERR2208944 | SKCM          |
| ERR2208928 | SKCM          |
| ERR2208949 | SKCM          |
| ERR2208900 | SKCM          |
| ERR2208922 | SKCM          |

After that, we need to map the cancer type to cancer code:

In [6]:

```
import json
cancer_code_map = pd.read_json('https://www.immuno-compass.com/download/other/cancer_code.json',
                               orient= 'index')[0]
df_cancer_type['cancer_type'] = df_cancer_type['cancer_type'].map(cancer_code_map)
df_cancer_type.head()
```

Out[6]:

|            |   cancer_type |
|------------|---------------|
| ERR2208944 |            25 |
| ERR2208928 |            25 |
| ERR2208949 |            25 |
| ERR2208900 |            25 |
| ERR2208922 |            25 |

#### Step2. Now lets map the df\_counts to Compass's input genes.¶

The dictionary below contains the gene Ensembl ID, Entrez gene ID, and gene name

In [7]:

```
gene_map = pd.read_csv('https://www.immuno-compass.com/download/other/compass_gene_map.csv')
gene_map.head()
```

Out[7]:

|    | ensid           | gene_name   | ensid_v36          | gene_type      | gene_supertype   |   entrezgene |
|----|-----------------|-------------|--------------------|----------------|------------------|--------------|
|  0 | ENSG00000121410 | A1BG        | ENSG00000121410.12 | protein_coding | protein_coding   |            1 |
|  1 | ENSG00000148584 | A1CF        | ENSG00000148584.15 | protein_coding | protein_coding   |        29974 |
|  2 | ENSG00000175899 | A2M         | ENSG00000175899.15 | protein_coding | protein_coding   |            2 |
|  3 | ENSG00000166535 | A2ML1       | ENSG00000166535.20 | protein_coding | protein_coding   |       144568 |
|  4 | ENSG00000128274 | A4GALT      | ENSG00000128274.17 | protein_coding | protein_coding   |        53947 |

In [8]:

```
df_tpm_input = df_tpm[gene_map.ensid_v36]
df_tpm_input.columns = df_tpm_input.columns.map(gene_map.set_index('ensid_v36').gene_name)
df_tpm_input.shape
```

Out[8]:

```
(25, 15672)
```

In [9]:

```
df_tpm_input.head()
```

Out[9]:

|            |     A1BG |     A1CF |      A2M |     A2ML1 |   A4GALT |    A4GNT |     AAAS |     AACS |    AADAC |   AADAT | ...   |   ZWILCH |   ZWINT |     ZXDA |     ZXDB |     ZXDC |   ZYG11A |   ZYG11B |     ZYX |   ZZEF1 |    ZZZ3 |
|------------|----------|----------|----------|-----------|----------|----------|----------|----------|----------|---------|-------|----------|---------|----------|----------|----------|----------|----------|---------|---------|---------|
| ERR2208944 | 0        | 0        |  859.204 | 73.0195   | 11.9423  | 1.94715  |  86.5275 |  9.23696 | 3.91852  | 17.764  | ...   |  16.7258 | 14.0124 |  7.25589 |  6.98473 | 16.4697  | 0.879873 |  21.1064 | 89.9209 | 47.521  | 21.5345 |
| ERR2208928 | 0.038627 | 0.032171 |  881.83  |  7.53352  | 12.6501  | 2.77854  |  95.1587 | 10.228   | 1.79836  | 10.8181 | ...   |  34.6131 | 42.5002 | 12.8067  | 12.3177  | 18.3576  | 0.526526 |  35.1632 | 60.7097 | 52.4134 | 25.8592 |
| ERR2208949 | 0.030213 | 0.012581 |  504.984 | 50.8369   |  5.90068 | 0.611231 | 106.174  |  8.09032 | 4.96013  | 28.5574 | ...   |  12.6773 | 19.6707 | 12.8369  |  9.51144 |  9.52844 | 0.154435 |  23.4249 | 69.7109 | 40.6383 | 25.9264 |
| ERR2208900 | 0.143579 | 0.023916 | 1940.42  |  0.18294  |  4.01477 | 0.813332 |  46.2254 |  2.23527 | 0.042219 | 40.343  | ...   |  24.998  | 21.5273 |  8.88971 |  7.7193  | 12.8137  | 0.670318 |  18.4525 | 56.5632 | 36.5421 | 32.3266 |
| ERR2208922 | 0.109992 | 0.286277 | 1534.68  |  0.860539 | 11.8872  | 0.061813 |  83.5377 |  5.87565 | 1.48236  | 16.9945 | ...   |  34.388  | 26.3693 |  3.39545 |  5.48517 |  6.93186 | 0.093706 |  19.7925 | 38.5398 | 25.9269 | 27.0028 |

5 rows × 15672 columns

In [10]:

```
#### Step3. Generate the inputs and save them
df_inputs = df_cancer_type.join(df_tpm_input)
df_inputs.head()
```

Out[10]:

|            |   cancer_type |     A1BG |     A1CF |      A2M |     A2ML1 |   A4GALT |    A4GNT |     AAAS |     AACS |    AADAC | ...   |   ZWILCH |   ZWINT |     ZXDA |     ZXDB |     ZXDC |   ZYG11A |   ZYG11B |     ZYX |   ZZEF1 |    ZZZ3 |
|------------|---------------|----------|----------|----------|-----------|----------|----------|----------|----------|----------|-------|----------|---------|----------|----------|----------|----------|----------|---------|---------|---------|
| ERR2208944 |            25 | 0        | 0        |  859.204 | 73.0195   | 11.9423  | 1.94715  |  86.5275 |  9.23696 | 3.91852  | ...   |  16.7258 | 14.0124 |  7.25589 |  6.98473 | 16.4697  | 0.879873 |  21.1064 | 89.9209 | 47.521  | 21.5345 |
| ERR2208928 |            25 | 0.038627 | 0.032171 |  881.83  |  7.53352  | 12.6501  | 2.77854  |  95.1587 | 10.228   | 1.79836  | ...   |  34.6131 | 42.5002 | 12.8067  | 12.3177  | 18.3576  | 0.526526 |  35.1632 | 60.7097 | 52.4134 | 25.8592 |
| ERR2208949 |            25 | 0.030213 | 0.012581 |  504.984 | 50.8369   |  5.90068 | 0.611231 | 106.174  |  8.09032 | 4.96013  | ...   |  12.6773 | 19.6707 | 12.8369  |  9.51144 |  9.52844 | 0.154435 |  23.4249 | 69.7109 | 40.6383 | 25.9264 |
| ERR2208900 |            25 | 0.143579 | 0.023916 | 1940.42  |  0.18294  |  4.01477 | 0.813332 |  46.2254 |  2.23527 | 0.042219 | ...   |  24.998  | 21.5273 |  8.88971 |  7.7193  | 12.8137  | 0.670318 |  18.4525 | 56.5632 | 36.5421 | 32.3266 |
| ERR2208922 |            25 | 0.109992 | 0.286277 | 1534.68  |  0.860539 | 11.8872  | 0.061813 |  83.5377 |  5.87565 | 1.48236  | ...   |  34.388  | 26.3693 |  3.39545 |  5.48517 |  6.93186 | 0.093706 |  19.7925 | 38.5398 | 25.9269 | 27.0028 |

5 rows × 15673 columns

In [11]:

```
df_inputs.to_csv('./compass_inputs.csv')
```