{"cells": [{"cell_type": "code", "execution_count": 26, "id": "017d64e3-253c-4f71-b1b6-8a9e2f29e970", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 27, "id": "5649e387-b1ca-4f26-b536-054b124fdb25", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('./conception.tsv',sep='\\t', index_col=0)"]}, {"cell_type": "code", "execution_count": 28, "id": "06d17c05-1c53-49a4-be0d-e1e5a20b8914", "metadata": {}, "outputs": [], "source": ["TOKEN_IDX_MAP = pd.read_json('./gene_tokens_long.json', orient='index').reset_index().set_index(0)['index'].astype(int).to_dict()\n", "def _genes_to_idxs(genes):\n", "\n", "    gene_idxs = []\n", "    for i in genes.split(':'):\n", "        idx = TOKEN_IDX_MAP.get(i)\n", "        if idx != None:\n", "            gene_idxs.append(i)\n", "    return ':'.join(gene_idxs)\n", "\n", "df['Genes'] = df['Genes'].apply(_genes_to_idxs)"]}, {"cell_type": "code", "execution_count": 29, "id": "dd5111bb-e95f-480e-8845-f1654a913e5d", "metadata": {}, "outputs": [], "source": ["df['n_genes'] = df['Genes'].apply(lambda x:x.split(':')).apply(lambda x:len(x))"]}, {"cell_type": "code", "execution_count": 30, "id": "6a53e851-1175-430f-bd4f-abab96ca472d", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["BroadCelltypePathway\n", "Memory_Bcell              3\n", "Stem                      4\n", "Pericyte                  5\n", "Naive_Bcell               5\n", "Hepatocyte                5\n", "Pneumocyte                6\n", "Plasma_cell               6\n", "Mesothelial               6\n", "Platelet                  7\n", "pDC                       7\n", "Erythrocyte               8\n", "Mast                      8\n", "Naive_Tcell               9\n", "Adipocyte                 9\n", "Endothelial              13\n", "Immune_checkpoint        13\n", "Bcell_general            14\n", "TLS                      15\n", "Memory_Tcell             15\n", "Apoptosis_pathway        16\n", "Pancreatic               18\n", "Fibroblast               19\n", "Epithelial               19\n", "Stroma                   21\n", "Granulocyte              24\n", "NKcell                   25\n", "Monocyte                 25\n", "Reference                28\n", "CD4_Tcell                31\n", "Treg                     32\n", "cDC                      38\n", "Cytotoxic_Tcell          40\n", "Myeloid                  40\n", "IFNg_pathway             41\n", "Cell_proliferation       47\n", "Innate_lymphoid_cell     51\n", "Tcell_general            51\n", "TGFb_pathway             56\n", "Genome_integrity         64\n", "Macrophage               77\n", "CD8_Tcell                95\n", "Exhausted_Tcell          95\n", "Cytokine                172\n", "Name: n_genes, dtype: int64"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby('BroadCelltypePathway').n_genes.sum().sort_values()"]}, {"cell_type": "code", "execution_count": 31, "id": "06d9269a-a51f-4c53-80d2-1a53ffee7db1", "metadata": {}, "outputs": [], "source": ["df.to_csv('./conception_processed.tsv',sep='\\t')"]}, {"cell_type": "code", "execution_count": null, "id": "5c699fe5-2896-4658-b259-c3cf2ee2d984", "metadata": {"scrolled": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}