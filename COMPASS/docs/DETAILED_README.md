# COMPASS: Comprehensive Omics Model for Predicting Across-cancer Survival and Sensitivity

## What is COMPASS?

COMPASS (Comprehensive Omics Model for Predicting Across-cancer Survival and Sensitivity) is a generalizable AI framework designed to predict immunotherapy outcomes across different cancer types and treatments. It represents a significant advancement in precision oncology by leveraging gene expression data to predict patient response to immune checkpoint inhibitors (ICIs).

COMPASS is a deep learning framework that transforms raw gene expression data into biologically meaningful representations at multiple levels (gene, gene-set, and cell/pathway levels), enabling accurate prediction of immunotherapy response regardless of cancer type or specific treatment.

## Key Features

- **Cross-cancer Generalizability**: Works across multiple cancer types without requiring cancer-specific retraining
- **Treatment-agnostic**: Predicts response to various immunotherapy treatments (anti-PD1, anti-CTLA4, combination therapies)
- **Biologically Interpretable**: Provides insights into the biological mechanisms driving treatment response
- **Hierarchical Representation**: Processes data at gene, gene-set, and cell/pathway levels
- **Transformer-based Architecture**: Utilizes attention mechanisms to capture complex gene interactions
- **Pre-training and Fine-tuning**: Employs a two-stage learning approach for robust performance

## Data Input Requirements

COMPASS requires gene expression data in the form of Transcripts Per Million (TPM) values. The input data should be structured as follows:

1. **Format**: Tab-separated values (TSV) file
2. **Structure**:
   - First column: Cancer code (e.g., "SKCM" for skin cutaneous melanoma)
   - Remaining columns: Gene expression values for 15,672 genes
   - Each row represents one patient sample

The framework expects normalized gene expression data, typically from RNA-seq experiments. The data preprocessing pipeline typically involves:

1. **Raw FASTQ files from RNA sequencing**:
   - Obtained from tumor biopsy samples using next-generation sequencing platforms
   - Typically paired-end reads with a minimum depth of 20-30 million reads per sample
   - Sequencing quality should have Q30 > 80% for reliable gene expression quantification

2. **Quality control and adapter trimming**:
   - Tools like FastQC for quality assessment and Trimmomatic or Cutadapt for trimming
   - Removal of low-quality bases (Phred score < 20) and adapter sequences
   - Filtering out reads shorter than 50bp after trimming to ensure reliable alignment

3. **Alignment to reference genome**:
   - Alignment to human reference genome (GRCh38/hg38 recommended)
   - STAR or HISAT2 aligners are preferred for their accuracy and speed
   - Minimum mapping quality threshold of 20 to ensure specificity

4. **Quantification of gene expression (TPM values)**:
   - Transcript quantification using tools like Salmon, Kallisto, or RSEM
   - Conversion to gene-level TPM (Transcripts Per Million) values
   - TPM normalization accounts for both sequencing depth and gene length
   - Log2(TPM+1) transformation is applied to reduce the effect of outliers

5. **Normalization and batch correction**:
   - Quantile normalization to ensure comparable distributions across samples
   - Batch effect correction using ComBat or similar methods if samples are from multiple sources
   - Filtering of low-expressed genes (typically those with TPM < 1 in >80% of samples)
   - Imputation of missing values using k-nearest neighbors or similar methods

Detailed instructions for generating appropriate input data can be found at the [COMPASS Data Pre-Processing Guide](https://www.immuno-compass.com/help/index.html).

## Feature Generation

COMPASS processes gene expression data through a sophisticated hierarchical architecture:

### 1. Gene-level Representation
- **Tokenization Process**:
  - Input gene expression values (15,672 genes) are converted into numerical tokens
  - Each gene is assigned a unique token ID based on predefined gene token mappings
  - Expression values are normalized and scaled to appropriate ranges for model input
  - Special tokens are added for cancer type and patient ID

- **Transformer Encoder Processing**:
  - A multi-head self-attention transformer encoder (similar to BERT architecture) processes the gene tokens
  - The encoder has configurable parameters: embedding dimension (default: 32), number of layers (default: 1-2), and attention heads (default: 2)
  - Positional embeddings capture the relative importance and relationships between genes
  - Dropout (typically 0.1-0.2) is applied during training to prevent overfitting

- **Special Token Integration**:
  - Cancer-type information is incorporated as a special token at the beginning of the sequence
  - Cancer types are encoded using a learned embedding matrix with 33 different cancer types
  - Patient-specific information is captured through a learnable token that acts as a global context
  - These special tokens help the model adapt to cancer-specific and patient-specific patterns

- **Output Representation**:
  - The transformer encoder outputs a sequence of vectors (one per gene plus special tokens)
  - Each gene is now represented by a context-aware embedding vector (dimension: 32)
  - These embeddings capture complex interactions between genes in the expression profile

### 2. Gene-set Level Representation
- **Biological Gene Set Definition**:
  - Gene sets are predefined groups of functionally related genes (e.g., immune pathways, metabolic pathways)
  - COMPASS uses 133 curated gene sets derived from established databases like MSigDB, KEGG, and GO
  - Each gene set contains genes that participate in the same biological process or pathway
  - Gene sets provide a biologically meaningful way to aggregate gene-level information

- **Aggregation Mechanisms**:
  - The `GeneSetAggregator` component uses several aggregation methods:
    - **Attention-based**: Weighted sum of gene embeddings where weights are learned through attention
    - **Pooling-based**: Max or mean pooling of gene embeddings within a set
    - **Linear projection**: Learned linear transformation of gene embeddings
  - Attention weights reveal which genes contribute most to each gene set's activity

- **Feature Scoring**:
  - The `GeneSetScorer` component converts aggregated embeddings into scalar scores
  - Scores represent the activity level of each gene set in the sample
  - Both vector representations (dim=32) and scalar scores are maintained for downstream use
  - These scores correlate with established pathway activity metrics but with higher sensitivity

- **Functional Relationship Capture**:
  - The model learns to identify coordinated expression patterns within gene sets
  - Captures synergistic and antagonistic relationships between genes in the same pathway
  - Reduces dimensionality while preserving biological signal (from 15,672 genes to 133 gene sets)

### 3. Cell/Pathway Level Representation
- **Cell Type and Pathway Mapping**:
  - The `CellPathwayProjector` component maps gene sets to 44 cell types and biological pathways
  - These include immune cell types (T cells, B cells, macrophages, etc.), stromal components, and key cancer pathways
  - The mapping is based on established markers and signatures from single-cell RNA-seq studies
  - Reference cell types are included to provide calibration points

- **Hierarchical Aggregation**:
  - Gene set features are aggregated into cell/pathway representations using attention mechanisms
  - The aggregation weights are learned during training and reveal which gene sets are most informative for each cell type
  - This creates a biologically interpretable hierarchy: genes → gene sets → cell types/pathways
  - The model can identify cell type-specific signals even from bulk RNA-seq data

- **Tumor Microenvironment Characterization**:
  - The resulting cell/pathway representations provide a comprehensive view of the tumor microenvironment
  - Captures immune infiltration patterns, stromal activity, and cancer cell intrinsic pathways
  - These features correlate with histopathological assessments and immunohistochemistry markers
  - Enables interpretation of model predictions in terms of biological mechanisms

- **Disentangled Representation**:
  - The `DisentangledProjector` ensures that different biological aspects are represented separately
  - Cancer-specific, patient-specific, and biology-specific features are separated
  - This disentanglement improves generalization across cancer types and treatments
  - Reference features help calibrate the model across different datasets

This hierarchical approach allows COMPASS to capture both low-level molecular details and high-level biological processes that influence immunotherapy response.

## Model Architecture

COMPASS employs a sophisticated architecture with several key components:

### 1. Input Encoder
- **TransformerEncoder**:
  - Core component that processes gene expression data using self-attention mechanisms
  - Implemented as a multi-layer transformer with configurable parameters:
    - `d_model`: Embedding dimension (default: 32)
    - `num_layers`: Number of transformer layers (default: 1-2)
    - `nhead`: Number of attention heads (default: 2)
    - `dim_feedforward`: Dimension of feedforward network (default: 64)
    - `dropout`: Dropout rate for regularization (default: 0.1-0.2)
  - Supports different encoder variants:
    - `transformer`: Standard transformer implementation
    - `flashformer`: Memory-efficient transformer variant
    - `cosformer`: Transformer with cosine attention for better long-sequence handling
    - `performer`: Linear attention transformer for efficiency with long sequences
  - Processes the entire sequence of gene tokens simultaneously, capturing global context

- **Positional Embeddings**:
  - Several positional embedding options are available:
    - `learnable`: Trainable position embeddings (default)
    - `gene2vect`: Pre-computed gene embeddings based on gene co-expression networks
    - `umap`: Embeddings derived from UMAP dimensionality reduction of gene expression data
  - Captures the relative importance and relationships between genes
  - Helps the model understand gene ordering and functional relationships
  - Critical for handling the permutation invariance problem in gene expression data

- **Cancer Type Embedding**:
  - Dedicated embedding layer for cancer types with 33 different cancer codes
  - Each cancer type has a unique embedding vector (dimension: 32)
  - Allows the model to learn cancer-specific patterns and adaptations
  - Implemented as `cancer_token_embedder` in the `TransformerEncoder` class
  - Enables cross-cancer generalization while preserving cancer-specific information

- **Patient ID Token**:
  - Special learnable token (`pid_token_embedder`) that acts as a global context vector
  - Initialized randomly and optimized during training
  - Serves as a "CLS" token similar to BERT, aggregating patient-level information
  - Helps capture patient-specific patterns that aren't explicitly encoded in gene expression

### 2. Latent Projector
- **DisentangledProjector**:
  - Core component that maps gene-level features to higher-level representations
  - Implements a hierarchical projection strategy with three main components:
    - `GeneSetProjector`: Projects to gene sets
    - `CellPathwayProjector`: Projects to cell types and pathways
    - `PatientProjector` and `CancerProjector`: Handle patient and cancer-specific features
  - Configuration parameters:
    - `proj_level`: Projection level ('geneset' or 'cellpathway', default: 'cellpathway')
    - `proj_pid`: Whether to project patient ID (default: True)
    - `proj_cancer_type`: Whether to project cancer type (default: True)
  - Maintains separation between different biological aspects (disentanglement)
  - Implemented with skip connections to preserve information flow

- **GeneSetProjector**:
  - Maps gene-level features to 133 predefined gene sets
  - Uses a combination of attention mechanisms and learned projections
  - Configuration options:
    - `geneset_agg_mode`: Aggregation mode ('attention', 'pooling', default: 'attention')
    - `geneset_score_mode`: Scoring mode ('linear', 'nonlinear', default: 'linear')
  - Maintains both vector representations and scalar scores for each gene set
  - Gene sets are defined based on biological knowledge from databases like MSigDB
  - Implemented in the `GeneSetProjector` class with configurable aggregation methods

- **CellPathwayProjector**:
  - Further aggregates gene-set features into 44 cell types and pathways
  - Uses a hierarchical attention mechanism to determine the importance of each gene set
  - Configuration options:
    - `cellpathway_agg_mode`: Aggregation mode ('attention', 'pooling', default: 'attention')
  - Cell types and pathways are defined based on established markers and signatures
  - Includes reference cell types for calibration across datasets
  - Implemented in the `CellPathwayProjector` class with configurable aggregation methods

- **Projection Flow**:
  - Input: Gene-level embeddings from the transformer encoder
  - Process: Hierarchical projection through gene sets to cell types/pathways
  - Output: Multi-level representations at gene, gene-set, and cell/pathway levels
  - Each level maintains both vector representations and scalar scores
  - The projection preserves biological interpretability throughout the hierarchy

### 3. Task Decoder
- **ClassDecoder**:
  - Specialized decoder for classification tasks (e.g., response prediction)
  - Architecture:
    - Multi-layer perceptron with configurable hidden layers
    - Batch normalization layers for stable training
    - ReLU activations and dropout for regularization
    - Softmax output layer for probability distribution
  - Configuration parameters:
    - `input_dim`: Dimension of input features (default: number of cell/pathway features)
    - `dense_layers`: List of hidden layer dimensions (default: [24])
    - `out_dim`: Output dimension (default: 2 for binary classification)
    - `batch_norms`: Whether to use batch normalization (default: True)
  - Optimized for immunotherapy response prediction (responder vs. non-responder)

- **RegDecoder**:
  - Specialized decoder for regression tasks (e.g., survival prediction)
  - Similar architecture to ClassDecoder but with linear output layer
  - Suitable for continuous outcome prediction such as:
    - Progression-free survival time
    - Overall survival time
    - Toxicity grade
  - Trained with mean absolute error or mean squared error loss functions

- **ProtoNetDecoder**:
  - Advanced decoder for few-shot learning scenarios
  - Implements prototypical network approach for learning from limited examples
  - Particularly useful for rare cancer types or novel treatment combinations
  - Can be initialized with support set examples for rapid adaptation
  - Configuration parameters:
    - `input_dim`: Dimension of input features
    - `out_dim`: Output dimension
    - `dense_layers`: List of hidden layer dimensions (can be empty for pure prototypical networks)
  - Enables transfer learning with minimal labeled examples

### 4. Training Components
- **TripletLoss**:
  - Contrastive loss function used during pre-training
  - Ensures similar samples have similar representations while pushing dissimilar samples apart
  - Configuration parameters:
    - `margin`: Minimum distance between positive and negative pairs (default: 1.0)
    - `metric`: Distance metric ('cosine', 'euclidean', default: 'cosine')
  - Implementation includes hard negative mining for efficient training
  - Critical for learning meaningful representations without labeled data
  - Helps the model capture biological similarities between samples

- **CEWithNaNLabelsLoss**:
  - Cross-entropy loss that handles missing labels (NaN values)
  - Particularly important for clinical datasets where some labels may be missing
  - Supports class weighting to handle imbalanced datasets
  - Configuration parameters:
    - `weights`: Class weights for handling imbalanced data (default: [1, 2] for binary classification)
  - Only computes loss for samples with available labels
  - Enables training on partially labeled datasets

- **MAEWithNaNLabelsLoss**:
  - Mean absolute error loss that handles missing labels
  - Used for regression tasks with potentially missing values
  - Similar to CEWithNaNLabelsLoss but for continuous outcomes
  - Particularly useful for survival analysis where some patients may be censored
  - Enables robust training on real-world clinical datasets with incomplete information

- **Additional Loss Components**:
  - `FocalLoss`: Addresses class imbalance by focusing on hard examples
  - `DiceLoss` and `DSCLoss`: Specialized losses for handling extreme imbalance
  - `HingeLoss`: Alternative loss function for binary classification
  - Batch correction loss term to minimize batch effects
  - Entropy regularization to prevent overconfident predictions

## Training Methodology

COMPASS employs a two-stage training approach:

### 1. Pre-training (Self-supervised Learning)
- Uses large-scale gene expression data from TCGA (The Cancer Genome Atlas)
- Employs contrastive learning to learn meaningful representations
- Captures general patterns in gene expression data across cancer types
- Implemented through the `PreTrainer` class

### 2. Fine-tuning (Supervised Learning)
- Adapts the pre-trained model to the specific task of immunotherapy response prediction
- Uses labeled data from immunotherapy clinical trials
- Supports different fine-tuning modes:
  - **FFT (Full Fine-Tuning)**: Updates all model parameters
  - **PFT (Partial Fine-Tuning)**: Updates only the projector and decoder
  - **LFT (Linear Fine-Tuning)**: Updates only the decoder (recommended for small datasets)
- Implemented through the `FineTuner` class

## Model Outputs

COMPASS provides several types of outputs:

1. **Response Prediction**: Binary classification (responder/non-responder)
   - Column `0`: Probability of non-response
   - Column `1`: Probability of response

2. **Feature Extraction**: Extracts biologically meaningful features at different levels
   - **Gene-level features** (15,672 features): Representation of individual gene activities
   - **Gene-set level features** (133 features): Representation of pathway activities
   - **Cell/pathway-level features** (44 features): Representation of cell type activities

These features can be used for downstream analyses such as:
- Building custom predictive models
- Patient stratification
- Biomarker discovery
- Survival analysis

## Usage Examples

### 1. Making Predictions

```python
import pandas as pd
from compass import loadcompass

# Load gene expression data
df_tpm = pd.read_csv('./data/compass_gide_tpm.tsv', sep='\t', index_col=0)

# Load pre-trained COMPASS model
model = loadcompass('./model/pft_leave_Gide.pt', map_location='cpu')

# Make predictions
_, df_pred = model.predict(df_tpm, batch_size=128)
```

### 2. Extracting Features

```python
# Load any COMPASS model
model = loadcompass('./model/pretrainer.pt')

# Extract features at different levels
dfgn, dfgs, dfct = model.extract(df_tpm, batch_size=128, with_gene_level=True)
```

### 3. Fine-tuning on Custom Data

```python
# Load pre-trained model
model = loadcompass('./model/finetuner_pft_all.pt')

# Set fine-tuning parameters
ft_args = {'mode': 'PFT', 'lr': 1e-3, 'batch_size': 16, 'max_epochs': 100, 'load_decoder': True}
finetuner = FineTuner(model, **ft_args)

# Load clinical data with response labels
df_cln = pd.read_csv('./data/compass_gide_clinical.tsv', sep='\t', index_col=0)
dfy = pd.get_dummies(df_cln.response_label)

# Fine-tune the model
finetuner.tune(df_tpm, dfy)
finetuner.save('./model/my_finetuner.pt')
```

## Advantages Over Baseline Methods

COMPASS offers several advantages over traditional immune scoring methods:

1. **Holistic representation**: Captures complex interactions between genes, pathways, and cell types
2. **Transfer learning**: Pre-training on large datasets enables robust performance on smaller clinical cohorts
3. **Cross-cancer applicability**: Works across multiple cancer types without requiring cancer-specific retraining
4. **Interpretability**: Provides insights into biological mechanisms driving treatment response
5. **Flexibility**: Can be fine-tuned for different prediction tasks (response, survival, toxicity)

## Conclusion

COMPASS represents a significant advancement in computational oncology by providing a generalizable framework for predicting immunotherapy outcomes across cancer types. By leveraging hierarchical representation learning and transformer-based architectures, it captures the complex biological processes that influence treatment response, enabling more precise patient stratification and treatment selection in clinical practice.

For more information, visit the [COMPASS website](https://www.immuno-compass.com/) or refer to the original publication: Wanxiang Shen, Thinh H. Nguyen, Michelle M. Li, Yepeng Huang, Intae Moon, Nitya Nair, Daniel Marbach‡, and Marinka Zitnik‡. *Generalizable AI predicts immunotherapy outcomes across cancers and treatments* [J]. [medRxiv](https://www.medrxiv.org/content/10.1101/2025.05.01.25326820).
