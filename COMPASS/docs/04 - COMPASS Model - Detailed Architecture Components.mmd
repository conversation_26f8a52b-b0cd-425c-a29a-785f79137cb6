graph TB
    %% Input Processing
    subgraph "Input Processing"
        A[Gene Expression Vector<br/>15,672 genes]
        B[Cancer Type Token<br/>0-32]
        C[Patient ID Token<br/>Learnable]
    end
    
    %% Input Encoder Details
    subgraph "Input Encoder - Transformer"
        D[Gene Token Embedder<br/>Gene → d_model]
        E[Cancer Token Embedder<br/>Cancer Type → d_model]
        F[Patient Token Embedder<br/>Learnable Embedding]
        G[Positional Encoding<br/>Learnable/Gene2Vec/UMAP]
        H[Multi-Head Attention<br/>Layers]
        I[Feed Forward<br/>Networks]
    end
    
    %% Latent Projector Details
    subgraph "Latent Projector - Disentangled"
        J[Gene Set Projector<br/>133 Gene Sets]
        K[Cell Pathway Projector<br/>44 Cell Types/Pathways]
        L[Patient Projector<br/>Patient-specific]
        M[Cancer Projector<br/>Cancer-specific]
    end
    
    %% Gene Set Processing
    subgraph "Gene Set Processing"
        N[Gene Set Aggregator<br/>Attention/Pooling]
        O[Gene Set Scorer<br/>Linear/Non-linear]
    end
    
    %% Cell Pathway Processing
    subgraph "Cell Pathway Processing"
        P[Cell Pathway Aggregator<br/>Attention/Pooling]
    end
    
    %% Task Decoder Options
    subgraph "Task Decoder"
        Q[Classification Decoder<br/>Dense + Softmax]
        R[Regression Decoder<br/>Dense + Linear]
        S[ProtoNet Decoder<br/>Few-shot Learning]
    end
    
    %% Data Flow
    A --> D
    B --> E
    C --> F
    D --> G
    E --> G
    F --> G
    G --> H
    H --> I
    I --> J
    I --> K
    I --> L
    I --> M
    
    J --> N
    N --> O
    K --> P
    
    O --> Q
    O --> R
    O --> S
    P --> Q
    P --> R
    P --> S
    
    %% Styling
    classDef inputClass fill:#e1f5fe
    classDef encoderClass fill:#e8f5e8
    classDef projectorClass fill:#f3e5f5
    classDef processingClass fill:#fff3e0
    classDef decoderClass fill:#fce4ec
    
    class A,B,C inputClass
    class D,E,F,G,H,I encoderClass
    class J,K,L,M projectorClass
    class N,O,P processingClass
    class Q,R,S decoderClass