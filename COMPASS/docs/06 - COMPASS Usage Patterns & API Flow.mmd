graph TB
    %% Main Classes
    subgraph "Main Framework Classes"
        A[PreTrainer<br/>Self-supervised learning]
        B[Adapter<br/>Feature-specific adaptation]
        C[FineTuner<br/>Task-specific fine-tuning]
    end
    
    %% PreTrainer Workflow
    subgraph "PreTrainer Workflow"
        D[Initialize PreTrainer<br/>Set hyperparameters]
        E[Load TCGA Data<br/>Gene expression + labels]
        F[Train Model<br/>Self-supervised + optional task]
        G[Save Pretrained Model<br/>General representations]
    end
    
    %% Adapter Workflow
    subgraph "Adapter Workflow"
        H[Load PreTrainer<br/>Pretrained model]
        I[Specify Target Feature<br/>TMB, PD-L1, etc.]
        J[Adapt Model<br/>Feature-specific tuning]
        K[Save Adapted Model<br/>Feature-enhanced]
    end
    
    %% FineTuner Workflow
    subgraph "FineTuner Workflow"
        L[Load PreTrainer/Adapter<br/>Base model]
        M[Set Fine-tuning Mode<br/>LFT/PFT/FFT]
        N[Load Task Data<br/>ITRP response data]
        O[Fine-tune Model<br/>Task-specific training]
        P[Save Final Model<br/>Task-optimized]
    end
    
    %% Inference & Extraction
    subgraph "Model Usage"
        Q[predict()<br/>Get task predictions]
        R[extract()<br/>Get multi-level features]
        S[project()<br/>Get vector representations]
        T[plot_embed()<br/>Visualize embeddings]
    end
    
    %% Data Flow
    A --> D
    D --> E
    E --> F
    F --> G
    
    A --> H
    G --> H
    H --> I
    I --> J
    J --> K
    
    A --> L
    G --> L
    K --> L
    L --> M
    M --> N
    N --> O
    O --> P
    
    G --> Q
    K --> Q
    P --> Q
    
    G --> R
    K --> R
    P --> R
    
    G --> S
    K --> S
    P --> S
    
    G --> T
    K --> T
    P --> T
    
    %% Configuration Options
    subgraph "Key Configuration"
        U[Encoder Type<br/>transformer/performer/etc.]
        V[Projection Level<br/>geneset/cellpathway]
        W[Loss Functions<br/>triplet/CE/MAE/focal]
        X[Augmentation<br/>mask/jitter/mix]
        Y[Fine-tuning Mode<br/>LFT/PFT/FFT]
    end
    
    %% Output Types
    subgraph "Output Types"
        Z[Task Predictions<br/>Response probability]
        AA[Gene Features<br/>15,672 dimensions]
        BB[Geneset Features<br/>133 dimensions]
        CC[Cellpathway Features<br/>44 dimensions]
        DD[Embeddings<br/>Vector representations]
    end
    
    Q --> Z
    R --> AA
    R --> BB
    R --> CC
    S --> DD
    
    %% Baseline Comparison
    subgraph "Baseline Methods"
        EE[CD8 Score<br/>T-cell marker]
        FF[PD-L1 Expression<br/>Checkpoint marker]
        GG[TMB Score<br/>Mutation burden]
        HH[TIDE Score<br/>Immune dysfunction]
        II[Other Immune Scores<br/>Various biomarkers]
    end
    
    %% Styling
    classDef mainClass fill:#e1f5fe
    classDef workflowClass fill:#e8f5e8
    classDef usageClass fill:#f3e5f5
    classDef configClass fill:#fff3e0
    classDef outputClass fill:#fce4ec
    classDef baselineClass fill:#f0f4c3
    
    class A,B,C mainClass
    class D,E,F,G,H,I,J,K,L,M,N,O,P workflowClass
    class Q,R,S,T usageClass
    class U,V,W,X,Y configClass
    class Z,AA,BB,CC,DD outputClass
    class EE,FF,GG,HH,II baselineClass