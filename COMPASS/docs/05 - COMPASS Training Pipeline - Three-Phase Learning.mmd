graph TB
    %% Phase 1: Pre-training
    subgraph "Phase 1: Pre-training (Self-Supervised)"
        A[TCGA Data<br/>Large-scale Gene Expression]
        B[Data Augmentation<br/>Mask/Jitter/Mix]
        C[Triplet Generation<br/>Anchor-Positive-Negative]
        D[Triplet Loss<br/>Contrastive Learning]
        E[Task Loss<br/>Optional Supervision]
        F["Combined Loss<br/>alpha*Task + (1-alpha)*SSL"]
        G[Pre-trained Model<br/>General Representations]
    end
    
    %% Phase 2: Adaptation
    subgraph "Phase 2: Adaptation (Feature-Specific)"
        H[Target Feature<br/>e.g., TMB, PD-L1]
        I[Freeze Encoder<br/>Keep Pre-trained Weights]
        J[Unfreeze Specific<br/>Gene Set/Pathway]
        K[MAE Loss<br/>Feature Prediction]
        L[Adapted Model<br/>Feature-Specific]
    end
    
    %% Phase 3: Fine-tuning
    subgraph "Phase 3: Fine-tuning (Task-Specific)"
        M[ITRP Data<br/>Immunotherapy Response]
        N[Fine-tuning Mode<br/>LFT/PFT/FFT]
        O[Task-Specific Loss<br/>CE/Focal/MAE/Dice]
        P[SSL Loss<br/>Optional Triplet]
        Q["Combined Loss<br/>alpha*Task + (1-alpha)*SSL"]
        R[Fine-tuned Model<br/>Task-Specific]
    end
    
    %% Training Flow
    A --> B
    B --> C
    C --> D
    D --> F
    E --> F
    F --> G
    
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    
    L --> M
    M --> N
    N --> O
    O --> Q
    P --> Q
    Q --> R
    
    %% Fine-tuning Modes
    subgraph "Fine-tuning Modes"
        S[LFT: Linear Fine-tuning<br/>Only Task Decoder]
        T[PFT: Partial Fine-tuning<br/>Projector + Decoder]
        U[FFT: Full Fine-tuning<br/>All Components]
    end
    
    N --> S
    N --> T
    N --> U
    
    %% Loss Functions
    subgraph "Loss Functions"
        V[TripletLoss<br/>Contrastive SSL]
        W[CEWithNaNLabelsLoss<br/>Classification]
        X[MAEWithNaNLabelsLoss<br/>Regression]
        Y[FocalLoss<br/>Class Imbalance]
        Z[DiceLoss<br/>Segmentation-like]
    end
    
    %% Styling
    classDef pretrainClass fill:#e1f5fe
    classDef adaptClass fill:#e8f5e8
    classDef finetuneClass fill:#f3e5f5
    classDef modeClass fill:#fff3e0
    classDef lossClass fill:#fce4ec
    
    class A,B,C,D,E,F,G pretrainClass
    class H,I,J,K,L adaptClass
    class M,N,O,P,Q,R finetuneClass
    class S,T,U modeClass
    class V,W,X,Y,Z lossClass