graph TB
    %% Input Features
    subgraph "Input Layer"
        A[Gene Expression<br/>15,672 genes]
        B[Cancer Type<br/>33 types]
        C[Patient ID<br/>Learnable token]
    end
    
    %% Transformer Encoding
    subgraph "Transformer Encoding"
        D[Gene Embeddings<br/>Gene × d_model]
        E[Cancer Embeddings<br/>Cancer × d_model]
        F[Patient Embeddings<br/>Patient × d_model]
        G[Concatenated Input<br/>[Patient, Cancer, Genes]]
        H[Multi-Head Attention<br/>Self-attention layers]
        I[Encoded Features<br/>Contextual representations]
    end
    
    %% Hierarchical Projection
    subgraph "Gene Set Level (133 sets)"
        J[Gene Set Aggregation<br/>Attention/Pooling]
        K[Gene Set Scoring<br/>Linear transformation]
        L[Gene Set Features<br/>133 dimensions]
    end
    
    subgraph "Cell Pathway Level (44 pathways)"
        M[Cell Pathway Aggregation<br/>From gene sets]
        N[Cell Pathway Features<br/>44 dimensions]
    end
    
    %% Additional Projections
    subgraph "Additional Projections"
        O[Patient-specific<br/>Projection]
        P[Cancer-specific<br/>Projection]
    end
    
    %% Final Feature Combinations
    subgraph "Combined Features"
        Q[Geneset + Patient + Cancer<br/>Combined representation]
        R[Cellpathway + Patient + Cancer<br/>Combined representation]
    end
    
    %% Task-Specific Outputs
    subgraph "Task Decoders"
        S[Classification Head<br/>Softmax output]
        T[Regression Head<br/>Linear output]
        U[Few-shot Head<br/>Prototype-based]
    end
    
    %% Feature Extraction Outputs
    subgraph "Extractable Features"
        V[Gene-level Features<br/>15,672 × d_model]
        W[Geneset-level Features<br/>133 scalar/vector]
        X[Cellpathway-level Features<br/>44 scalar/vector]
        Y[Task Predictions<br/>Class probabilities/values]
    end
    
    %% Data Flow
    A --> D
    B --> E
    C --> F
    D --> G
    E --> G
    F --> G
    G --> H
    H --> I
    
    I --> J
    J --> K
    K --> L
    L --> M
    M --> N
    
    I --> O
    I --> P
    
    L --> Q
    O --> Q
    P --> Q
    
    N --> R
    O --> R
    P --> R
    
    Q --> S
    Q --> T
    Q --> U
    R --> S
    R --> T
    R --> U
    
    I --> V
    L --> W
    N --> X
    S --> Y
    T --> Y
    U --> Y
    
    %% Gene Set Details
    subgraph "Gene Set Examples"
        Z[Immune Response<br/>T-cell activation]
        AA[Metabolism<br/>Glycolysis pathway]
        BB[Cell Cycle<br/>DNA replication]
        CC[Apoptosis<br/>Cell death]
    end
    
    %% Cell Pathway Details
    subgraph "Cell Pathway Examples"
        DD[CD8+ T cells<br/>Cytotoxic lymphocytes]
        EE[Macrophages<br/>Immune effectors]
        FF[Cancer cells<br/>Tumor characteristics]
        GG[Stromal cells<br/>Microenvironment]
    end
    
    %% Styling
    classDef inputClass fill:#e1f5fe
    classDef encoderClass fill:#e8f5e8
    classDef genesetClass fill:#f3e5f5
    classDef cellpathwayClass fill:#fff3e0
    classDef projectionClass fill:#fce4ec
    classDef combinedClass fill:#f0f4c3
    classDef decoderClass fill:#e0e0e0
    classDef outputClass fill:#ffebee
    classDef exampleClass fill:#f5f5f5
    
    class A,B,C inputClass
    class D,E,F,G,H,I encoderClass
    class J,K,L genesetClass
    class M,N cellpathwayClass
    class O,P projectionClass
    class Q,R combinedClass
    class S,T,U decoderClass
    class V,W,X,Y outputClass
    class Z,AA,BB,CC,DD,EE,FF,GG exampleClass