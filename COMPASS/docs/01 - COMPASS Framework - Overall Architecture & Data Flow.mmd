graph TB
    %% Input Data
    subgraph "Input Data"
        A[Gene Expression Data<br/>TPM Matrix]
        B[Clinical Labels<br/>Response/Survival]
        C[Cancer Type<br/>Annotations]
    end
    
    %% Data Processing
    subgraph "Data Processing Pipeline"
        D[Data Scaling<br/>MinMax/Standard]
        E[Data Augmentation<br/>Mask/Jitter/Mix]
        F[Dataset Creation<br/>TCGAData/ITRPData]
    end
    
    %% Core Model Architecture
    subgraph "COMPASS Model Architecture"
        G[Input Encoder<br/>Transformer]
        H[Latent Projector<br/>Disentangled]
        I[Task Decoder<br/>Classification/Regression]
    end
    
    %% Training Phases
    subgraph "Training Phases"
        J[Pre-training<br/>Self-Supervised]
        K[Adaptation<br/>Feature-Specific]
        L[Fine-tuning<br/>Task-Specific]
    end
    
    %% Outputs
    subgraph "Model Outputs"
        M[Gene-level Features<br/>15,672 dims]
        N[Geneset-level Features<br/>133 dims]
        O[Cell Pathway Features<br/>44 dims]
        P[Task Predictions<br/>Response/Survival]
    end
    
    %% Data Flow
    A --> D
    B --> F
    C --> F
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    H --> M
    H --> N
    H --> O
    I --> P
    
    %% Training Flow
    F --> J
    J --> K
    K --> L
    
    %% Styling
    classDef inputClass fill:#e1f5fe
    classDef processClass fill:#f3e5f5
    classDef modelClass fill:#e8f5e8
    classDef trainingClass fill:#fff3e0
    classDef outputClass fill:#fce4ec
    
    class A,B,C inputClass
    class D,E,F processClass
    class G,H,I modelClass
    class J,K,L trainingClass
    class M,N,O,P outputClass