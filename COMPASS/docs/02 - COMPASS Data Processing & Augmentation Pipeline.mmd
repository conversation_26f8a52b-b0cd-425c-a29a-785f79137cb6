graph TB
    %% Raw Data Input
    subgraph "Raw Data Input"
        A[Gene Expression Matrix<br/>Samples × Genes]
        B[Clinical Labels<br/>Response/Survival]
        C[Cancer Type Labels<br/>33 Cancer Types]
    end
    
    %% Data Scaling
    subgraph "Data Scaling"
        D[Datascaler<br/>MinMax/Standard/None]
        E[Scaled Gene Expression<br/>Normalized Values]
    end
    
    %% Augmentation Methods
    subgraph "Data Augmentation"
        F[RandomMaskAugmentor<br/>Zero out genes randomly]
        G[FeatureJitterAugmentor<br/>Add Gaussian noise]
        H[MaskJitterAugmentor<br/>Combined mask + jitter]
    end
    
    %% Dataset Creation
    subgraph "Dataset Creation"
        I[TCGAData<br/>Pre-training Dataset]
        J[ITRPData<br/>Fine-tuning Dataset]
        K[GeneData<br/>Inference Dataset]
    end
    
    %% Triplet Generation
    subgraph "Triplet Generation (TCGAData)"
        L[Anchor Sample<br/>Original]
        M[Positive Sample<br/>Augmented Anchor]
        N[Negative Sample<br/>Augmented KNN]
        O[K-Nearest Neighbors<br/>Distance-based]
    end
    
    %% Data Loaders
    subgraph "Data Loaders"
        P[Training Loader<br/>Batch Processing]
        Q[Validation Loader<br/>Evaluation]
        R[Test Loader<br/>Final Testing]
    end
    
    %% Data Flow
    A --> D
    D --> E
    E --> F
    E --> G
    E --> H
    
    F --> I
    G --> I
    H --> I
    E --> J
    E --> K
    
    B --> I
    B --> J
    C --> I
    C --> J
    
    I --> L
    L --> M
    L --> N
    O --> N
    
    I --> P
    J --> P
    K --> P
    I --> Q
    J --> Q
    I --> R
    J --> R
    
    %% Augmentation Details
    subgraph "Augmentation Parameters"
        S[Mask Probability<br/>0.01-0.1]
        T[Jitter Standard Dev<br/>0.01-0.1]
        U[No Augment Probability<br/>0.1]
        V[Number of Views<br/>1-2]
    end
    
    F --> S
    G --> T
    H --> S
    H --> T
    F --> U
    G --> U
    H --> U
    F --> V
    G --> V
    H --> V
    
    %% Styling
    classDef inputClass fill:#e1f5fe
    classDef scalingClass fill:#e8f5e8
    classDef augmentClass fill:#f3e5f5
    classDef datasetClass fill:#fff3e0
    classDef tripletClass fill:#fce4ec
    classDef loaderClass fill:#f0f4c3
    classDef paramClass fill:#e0e0e0
    
    class A,B,C inputClass
    class D,E scalingClass
    class F,G,H augmentClass
    class I,J,K datasetClass
    class L,M,N,O tripletClass
    class P,Q,R loaderClass
    class S,T,U,V paramClass