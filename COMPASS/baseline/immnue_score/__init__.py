from .PD1 import PD1
from .PDL1 import <PERSON>L1
from .CTLA4 import <PERSON>LA4
from .GeneBio import GeneBio
from .Kong_NetBio import Kong_NetB<PERSON>

from .Wu_MIAS import <PERSON><PERSON><PERSON><PERSON>
from .Cristescu_GEP import <PERSON><PERSON><PERSON>_<PERSON><PERSON>

from .Auslander_IMPRES import <PERSON><PERSON><PERSON>_IMPRES

from .Jiang_TIDE import <PERSON><PERSON><PERSON><PERSON><PERSON>

from .Huang_NRS import <PERSON><PERSON><PERSON><PERSON>
from .Ayers_IFNG import Ayers_<PERSON><PERSON>

from .CD8 import CD8
from .Davoli_CIS import <PERSON><PERSON><PERSON>_CIS
from .Roh_IS import Roh_IS
from .Fehrenbacher_Teff import <PERSON><PERSON><PERSON><PERSON>_Te<PERSON>


from .Jiang_CTLs import Jiang_CTLs
from .Jiang_TAMs import <PERSON>_TAMs
from .Jiang_Texh import <PERSON>_<PERSON>h


from .Messina_CKS import <PERSON><PERSON><PERSON>_<PERSON><PERSON>
from .Nurmik_CAFs import Nurmik_CAFs

from .Rooney_ICA import Rooney_ICA
from .Freeman_PGM import Freeman_PGM


immnue_score_methods = {'PD1':PD1, 'PDL1':PDL1, 'CTLA4':CTLA4,  'CD8':CD8,  'GeneBio':<PERSON><PERSON><PERSON>,  'NetBio':Kong_NetBio,
                        'MIAS': <PERSON><PERSON><PERSON><PERSON>, 'GEP':<PERSON><PERSON><PERSON>_GEP, 'IMPRES':<PERSON><PERSON>nder_IMPRES, 'TIDE':Jiang_TIDE,
                        'NRS': Huang_NRS, 'IFNG':Ayers_IFNG, 'CIS': Davoli_CIS, 'IS': Roh_IS, 'Teff': Fehrenbacher_Teff, 'PGM':Freeman_PGM,
                        'CKS': Messina_CKS, 'CAF': Nurmik_CAFs, 'CTL': Jiang_CTLs, 'TAM': Jiang_TAMs, 'Texh':Jiang_Texh, 'ICA':Rooney_ICA}



