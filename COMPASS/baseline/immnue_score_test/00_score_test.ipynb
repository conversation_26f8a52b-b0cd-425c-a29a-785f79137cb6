{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2d3c5163-4185-4454-b184-b64449b4b56c", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import os\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "import sys\n", "sys.path.insert(0, '/home/<USER>/Research/mims-compass/')\n", "from baseline.immnue_score import immnue_score_methods"]}, {"cell_type": "code", "execution_count": 2, "id": "79fc0bb2-7778-437b-9277-e84e86f2b241", "metadata": {}, "outputs": [], "source": ["# from baseline.immnue_score import markers\n", "\n", "# # map gene name to Entrez ID\n", "# name2entrez_map = markers.GENE_ID_MAP.set_index('gene_name')['entrezgene']"]}, {"cell_type": "code", "execution_count": null, "id": "3c0896e8-9890-40d1-9ddd-948830801f12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "ba2ffccb-4135-4e16-a822-c0dd305908ca", "metadata": {}, "outputs": [], "source": ["df_tpm = pd.read_csv('./toy_data.csv', index_col=0)"]}, {"cell_type": "code", "execution_count": 4, "id": "5c471dd4-aa8a-4a74-ae14-6aa57250ffb7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A1BG</th>\n", "      <th>A1CF</th>\n", "      <th>A2M</th>\n", "      <th>A2ML1</th>\n", "      <th>A4GALT</th>\n", "      <th>A4GNT</th>\n", "      <th>AAAS</th>\n", "      <th>AACS</th>\n", "      <th>AADAC</th>\n", "      <th>AADAT</th>\n", "      <th>...</th>\n", "      <th>ZWILCH</th>\n", "      <th>ZWINT</th>\n", "      <th>ZXDA</th>\n", "      <th>ZXDB</th>\n", "      <th>ZXDC</th>\n", "      <th>ZYG11A</th>\n", "      <th>ZYG11B</th>\n", "      <th>ZYX</th>\n", "      <th>ZZEF1</th>\n", "      <th>ZZZ3</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Pt1</th>\n", "      <td>61.80</td>\n", "      <td>0.00</td>\n", "      <td>313.62</td>\n", "      <td>0.02</td>\n", "      <td>11.98</td>\n", "      <td>0.04</td>\n", "      <td>38.35</td>\n", "      <td>4.93</td>\n", "      <td>0.08</td>\n", "      <td>4.16</td>\n", "      <td>...</td>\n", "      <td>6.14</td>\n", "      <td>23.56</td>\n", "      <td>1.71</td>\n", "      <td>3.44</td>\n", "      <td>11.37</td>\n", "      <td>0.01</td>\n", "      <td>7.97</td>\n", "      <td>101.62</td>\n", "      <td>7.18</td>\n", "      <td>30.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt2</th>\n", "      <td>539.67</td>\n", "      <td>16.09</td>\n", "      <td>512.49</td>\n", "      <td>0.11</td>\n", "      <td>0.46</td>\n", "      <td>0.09</td>\n", "      <td>22.54</td>\n", "      <td>2.77</td>\n", "      <td>45.51</td>\n", "      <td>7.21</td>\n", "      <td>...</td>\n", "      <td>2.98</td>\n", "      <td>7.62</td>\n", "      <td>1.10</td>\n", "      <td>3.19</td>\n", "      <td>5.80</td>\n", "      <td>0.06</td>\n", "      <td>3.90</td>\n", "      <td>34.36</td>\n", "      <td>3.52</td>\n", "      <td>14.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt4</th>\n", "      <td>29.00</td>\n", "      <td>0.00</td>\n", "      <td>65.58</td>\n", "      <td>0.02</td>\n", "      <td>0.62</td>\n", "      <td>0.01</td>\n", "      <td>24.20</td>\n", "      <td>1.80</td>\n", "      <td>0.08</td>\n", "      <td>1.76</td>\n", "      <td>...</td>\n", "      <td>8.41</td>\n", "      <td>20.81</td>\n", "      <td>0.65</td>\n", "      <td>2.33</td>\n", "      <td>7.15</td>\n", "      <td>0.00</td>\n", "      <td>2.14</td>\n", "      <td>48.40</td>\n", "      <td>6.00</td>\n", "      <td>8.99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt5</th>\n", "      <td>62.03</td>\n", "      <td>0.00</td>\n", "      <td>65.24</td>\n", "      <td>0.00</td>\n", "      <td>1.07</td>\n", "      <td>0.00</td>\n", "      <td>41.74</td>\n", "      <td>13.90</td>\n", "      <td>0.00</td>\n", "      <td>4.93</td>\n", "      <td>...</td>\n", "      <td>12.37</td>\n", "      <td>33.70</td>\n", "      <td>1.18</td>\n", "      <td>3.71</td>\n", "      <td>11.11</td>\n", "      <td>0.00</td>\n", "      <td>5.71</td>\n", "      <td>85.19</td>\n", "      <td>8.66</td>\n", "      <td>20.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt6</th>\n", "      <td>8.56</td>\n", "      <td>0.01</td>\n", "      <td>110.72</td>\n", "      <td>0.00</td>\n", "      <td>1.11</td>\n", "      <td>0.00</td>\n", "      <td>17.82</td>\n", "      <td>6.07</td>\n", "      <td>0.00</td>\n", "      <td>1.59</td>\n", "      <td>...</td>\n", "      <td>8.63</td>\n", "      <td>13.53</td>\n", "      <td>1.26</td>\n", "      <td>3.39</td>\n", "      <td>4.75</td>\n", "      <td>0.00</td>\n", "      <td>3.15</td>\n", "      <td>65.72</td>\n", "      <td>7.41</td>\n", "      <td>12.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt7</th>\n", "      <td>80.39</td>\n", "      <td>0.00</td>\n", "      <td>96.76</td>\n", "      <td>0.01</td>\n", "      <td>1.22</td>\n", "      <td>0.01</td>\n", "      <td>21.00</td>\n", "      <td>7.56</td>\n", "      <td>0.00</td>\n", "      <td>1.38</td>\n", "      <td>...</td>\n", "      <td>11.81</td>\n", "      <td>29.44</td>\n", "      <td>0.88</td>\n", "      <td>2.40</td>\n", "      <td>9.00</td>\n", "      <td>2.12</td>\n", "      <td>5.17</td>\n", "      <td>21.14</td>\n", "      <td>4.44</td>\n", "      <td>12.24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt8</th>\n", "      <td>37.22</td>\n", "      <td>0.00</td>\n", "      <td>182.00</td>\n", "      <td>0.16</td>\n", "      <td>4.91</td>\n", "      <td>0.00</td>\n", "      <td>25.68</td>\n", "      <td>6.85</td>\n", "      <td>0.00</td>\n", "      <td>1.83</td>\n", "      <td>...</td>\n", "      <td>8.70</td>\n", "      <td>12.02</td>\n", "      <td>0.73</td>\n", "      <td>1.55</td>\n", "      <td>5.83</td>\n", "      <td>0.02</td>\n", "      <td>4.25</td>\n", "      <td>128.65</td>\n", "      <td>6.40</td>\n", "      <td>14.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt9</th>\n", "      <td>36.93</td>\n", "      <td>0.00</td>\n", "      <td>525.24</td>\n", "      <td>0.52</td>\n", "      <td>4.96</td>\n", "      <td>0.07</td>\n", "      <td>31.75</td>\n", "      <td>7.04</td>\n", "      <td>0.75</td>\n", "      <td>3.17</td>\n", "      <td>...</td>\n", "      <td>9.93</td>\n", "      <td>25.73</td>\n", "      <td>1.43</td>\n", "      <td>2.94</td>\n", "      <td>7.05</td>\n", "      <td>0.01</td>\n", "      <td>5.97</td>\n", "      <td>76.64</td>\n", "      <td>8.17</td>\n", "      <td>11.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt10</th>\n", "      <td>30.58</td>\n", "      <td>0.00</td>\n", "      <td>731.42</td>\n", "      <td>0.04</td>\n", "      <td>40.94</td>\n", "      <td>0.03</td>\n", "      <td>33.68</td>\n", "      <td>12.50</td>\n", "      <td>0.03</td>\n", "      <td>13.21</td>\n", "      <td>...</td>\n", "      <td>6.72</td>\n", "      <td>38.71</td>\n", "      <td>0.82</td>\n", "      <td>1.52</td>\n", "      <td>10.76</td>\n", "      <td>0.03</td>\n", "      <td>5.15</td>\n", "      <td>107.05</td>\n", "      <td>8.09</td>\n", "      <td>13.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pt12</th>\n", "      <td>38.42</td>\n", "      <td>0.00</td>\n", "      <td>742.78</td>\n", "      <td>0.00</td>\n", "      <td>2.63</td>\n", "      <td>0.30</td>\n", "      <td>26.61</td>\n", "      <td>26.02</td>\n", "      <td>0.09</td>\n", "      <td>10.16</td>\n", "      <td>...</td>\n", "      <td>25.62</td>\n", "      <td>59.63</td>\n", "      <td>0.19</td>\n", "      <td>1.80</td>\n", "      <td>4.56</td>\n", "      <td>0.01</td>\n", "      <td>3.30</td>\n", "      <td>424.57</td>\n", "      <td>5.27</td>\n", "      <td>22.46</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 15672 columns</p>\n", "</div>"], "text/plain": ["         A1BG   A1CF     A2M  A2ML1  A4GALT  A4GNT   AAAS   AACS  AADAC  \\\n", "Index                                                                     \n", "Pt1     61.80   0.00  313.62   0.02   11.98   0.04  38.35   4.93   0.08   \n", "Pt2    539.67  16.09  512.49   0.11    0.46   0.09  22.54   2.77  45.51   \n", "Pt4     29.00   0.00   65.58   0.02    0.62   0.01  24.20   1.80   0.08   \n", "Pt5     62.03   0.00   65.24   0.00    1.07   0.00  41.74  13.90   0.00   \n", "Pt6      8.56   0.01  110.72   0.00    1.11   0.00  17.82   6.07   0.00   \n", "Pt7     80.39   0.00   96.76   0.01    1.22   0.01  21.00   7.56   0.00   \n", "Pt8     37.22   0.00  182.00   0.16    4.91   0.00  25.68   6.85   0.00   \n", "Pt9     36.93   0.00  525.24   0.52    4.96   0.07  31.75   7.04   0.75   \n", "Pt10    30.58   0.00  731.42   0.04   40.94   0.03  33.68  12.50   0.03   \n", "Pt12    38.42   0.00  742.78   0.00    2.63   0.30  26.61  26.02   0.09   \n", "\n", "       AADAT  ...  ZWILCH  ZWINT  ZXDA  ZXDB   ZXDC  ZYG11A  ZYG11B     ZYX  \\\n", "Index         ...                                                             \n", "Pt1     4.16  ...    6.14  23.56  1.71  3.44  11.37    0.01    7.97  101.62   \n", "Pt2     7.21  ...    2.98   7.62  1.10  3.19   5.80    0.06    3.90   34.36   \n", "Pt4     1.76  ...    8.41  20.81  0.65  2.33   7.15    0.00    2.14   48.40   \n", "Pt5     4.93  ...   12.37  33.70  1.18  3.71  11.11    0.00    5.71   85.19   \n", "Pt6     1.59  ...    8.63  13.53  1.26  3.39   4.75    0.00    3.15   65.72   \n", "Pt7     1.38  ...   11.81  29.44  0.88  2.40   9.00    2.12    5.17   21.14   \n", "Pt8     1.83  ...    8.70  12.02  0.73  1.55   5.83    0.02    4.25  128.65   \n", "Pt9     3.17  ...    9.93  25.73  1.43  2.94   7.05    0.01    5.97   76.64   \n", "Pt10   13.21  ...    6.72  38.71  0.82  1.52  10.76    0.03    5.15  107.05   \n", "Pt12   10.16  ...   25.62  59.63  0.19  1.80   4.56    0.01    3.30  424.57   \n", "\n", "       ZZEF1   ZZZ3  \n", "Index                \n", "Pt1     7.18  30.81  \n", "Pt2     3.52  14.03  \n", "Pt4     6.00   8.99  \n", "Pt5     8.66  20.79  \n", "Pt6     7.41  12.30  \n", "Pt7     4.44  12.24  \n", "Pt8     6.40  14.98  \n", "Pt9     8.17  11.92  \n", "Pt10    8.09  13.50  \n", "Pt12    5.27  22.46  \n", "\n", "[10 rows x 15672 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_tpm"]}, {"cell_type": "code", "execution_count": 5, "id": "741b1d21-a66e-4743-a478-fab4bce93f06", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'PD1': baseline.immnue_score.PD1.PD1,\n", " 'PDL1': baseline.immnue_score.PDL1.PDL1,\n", " 'CTLA4': baseline.immnue_score.CTLA4.CTLA4,\n", " 'CD8': baseline.immnue_score.CD8.CD8,\n", " 'GeneBio': baseline.immnue_score.GeneBio.GeneBio,\n", " 'NetBio': baseline.immnue_score.Kong_NetBio.Kong_NetBio,\n", " 'MIAS': baseline.immnue_score.Wu_MIAS.Wu_MIAS,\n", " 'GEP': baseline.immnue_score.<PERSON><PERSON><PERSON>_GEP.<PERSON><PERSON><PERSON>_GEP,\n", " 'IMPRES': baseline.immnue_score.Auslander_IMPRES.Auslander_IMPRES,\n", " 'TIDE': baseline.immnue_score.Jiang_TIDE.Jiang_TIDE,\n", " 'NRS': baseline.immnue_score.Huang_NRS.Huang_NRS,\n", " 'IFNG': baseline.immnue_score.Ayers_IFNG.Ayers_IFNG,\n", " 'CIS': baseline.immnue_score.Davoli_CIS.Davoli_CIS,\n", " 'IS': baseline.immnue_score.Roh_IS.Roh_IS,\n", " 'Teff': baseline.immnue_score.<PERSON><PERSON><PERSON>er_Teff.<PERSON><PERSON><PERSON><PERSON>_Te<PERSON>,\n", " 'PGM': baseline.immnue_score.Freeman_PGM.Freeman_PGM,\n", " 'CKS': baseline.immnue_score.Messina_CKS.Messina_CKS,\n", " 'CAF': baseline.immnue_score.Nurmik_CAFs.Nurmik_CAFs,\n", " 'CTL': baseline.immnue_score.Jiang_CTLs.Jiang_CTLs,\n", " 'TAM': baseline.immnue_score.Jiang_TAMs.Jiang_TAMs,\n", " 'Texh': baseline.immnue_score.Jiang_Texh.Jiang_<PERSON>,\n", " 'ICA': baseline.immnue_score.Rooney_ICA.Rooney_ICA}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["immnue_score_methods"]}, {"cell_type": "code", "execution_count": 6, "id": "0c3b7694-4c43-42cd-b138-dec2f6d45f40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PD1 <class 'baseline.immnue_score.PD1.PD1'>\n", "PDL1 <class 'baseline.immnue_score.PDL1.PDL1'>\n", "CTLA4 <class 'baseline.immnue_score.CTLA4.CTLA4'>\n", "CD8 <class 'baseline.immnue_score.CD8.CD8'>\n", "GeneBio <class 'baseline.immnue_score.GeneBio.GeneBio'>\n", "NetBio <class 'baseline.immnue_score.Kong_NetBio.Kong_NetBio'>\n", "MIAS <class 'baseline.immnue_score.Wu_MIAS.Wu_MIAS'>\n", "GEP <class 'baseline.immnue_score.<PERSON><PERSON><PERSON>_GEP.<PERSON><PERSON>_GEP'>\n", "IMPRES <class 'baseline.immnue_score.Auslander_IMPRES.Auslander_IMPRES'>\n", "TIDE <class 'baseline.immnue_score.Jiang_TIDE.Jiang_TIDE'>\n", "[WARN] The majority(>80%) of genes with positive expression in your inputted data. Please Normalize your data\n", "[WARN] Start normalizing the input expression profile by: 1. Do the log2(x+1) transformation. 2. Subtract the average across your samples.\n", "NRS <class 'baseline.immnue_score.Huang_NRS.Huang_NRS'>\n", "IFNG <class 'baseline.immnue_score.Ayers_IFNG.Ayers_IFNG'>\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Research/mims-conceptor/baseline/immnue_score/Ayers_IFNG.py:36: UserWarning: Markers of ['CCL5'] are missed and not used.\n", "  warnings.warn('Markers of %s are missed and not used.' % markers_unused)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CIS <class 'baseline.immnue_score.Davoli_CIS.Davoli_CIS'>\n", "IS <class 'baseline.immnue_score.Roh_IS.Roh_IS'>\n", "Teff <class 'baseline.immnue_score.<PERSON><PERSON>bacher_Teff.Fe<PERSON>bacher_Teff'>\n", "PGM <class 'baseline.immnue_score.Freeman_PGM.Freeman_PGM'>\n", "CKS <class 'baseline.immnue_score.Messina_CKS.Messina_CKS'>\n", "CAF <class 'baseline.immnue_score.Nurmik_CAFs.Nurmik_CAFs'>\n", "CTL <class 'baseline.immnue_score.Jiang_CTLs.Jiang_CTLs'>\n", "TAM <class 'baseline.immnue_score.Jiang_TAMs.Jiang_TAMs'>\n", "Texh <class 'baseline.immnue_score.Jiang_Texh.Jiang_Texh'>\n", "ICA <class 'baseline.immnue_score.Rooney_ICA.Rooney_ICA'>\n"]}], "source": ["for k, f in immnue_score_methods.items():\n", "    print(k, f)\n", "    F = f(cancer_type='SKCM', drug_target='PD1')\n", "    s1 = F(df_tpm)\n", "    s2 = F.get_avg(df_tpm)\n", "    s3 = F.get_org(df_tpm)\n", "    s4 = F.get_pca(df_tpm)\n", "    s5 = F.get_ssgsea(df_tpm)"]}, {"cell_type": "code", "execution_count": null, "id": "9fe83056-012f-4adf-a10d-ddb7de1e3a1d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "12e89eec-55e6-4092-bd47-e51a12e71024", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f0bcac76-c9b7-40f2-b17a-8b221a6e9f2b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "15a4b4d1-ff58-4341-b8b3-f6b11a2e1125", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "13cffe44-9d03-4c3c-b7fd-54bfa721aba1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 5}