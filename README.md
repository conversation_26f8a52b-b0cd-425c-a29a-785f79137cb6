# LLaOA: Large Language and Omics Assistant

[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![UV](https://img.shields.io/badge/uv-package%20manager-green.svg)](https://github.com/astral-sh/uv)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**LLaOA** is a multimodal AI assistant that combines large language models with COMPASS omics encoders to enable natural language interactions with genomic and transcriptomic data.

## 🔥 Key Features

- **🧬 Omics Integration**: Native support for RNA-seq, gene expression, and multi-omics data analysis
- **🤖 LLAMA + COMPASS**: Seamless integration between LLAMA language models and COMPASS omics encoders
- **🛠️ UV-Native**: Built for the UV package manager for fast, reliable dependency management
- **🎯 Multimodal Training**: Multiple training strategies from projector-only to full fine-tuning
- **📊 Rich Data Support**: Handles various omics data formats (H5, TSV, JSON)

## 🚀 Quick Start

### Installation with UV (Recommended)

```bash
# Install UV (if not already installed)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clone and setup LLaOA
git clone https://github.com/your-org/LLaOA.git
cd LLaOA

# Install dependencies and setup environment
uv sync
```

### Download Required Models

```bash
# Download COMPASS omics encoder
uv run python -c "
from llaoa.model.omics_encoder.compass_encoder import download_compass_model
download_compass_model('./models/compass_model')
"

# LLAMA models will be automatically downloaded from HuggingFace
```

### Basic Usage

```python
import uv
uv.run([
    "python", "run_train.py",
    "--model_path", "microsoft/DialoGPT-medium",
    "--omics_tower_path", "./models/compass_model",
    "--data_path", "./data/dataset_01/sample_data.json",
    "--output_dir", "./checkpoints/my_model"
])
```

## 📚 Language Model Support

- **LLAMA**: Meta's open-source LLM family (7B, 13B, 70B parameter variants) - the primary and recommended language model for LLaOA

## 🧬 Omics Encoder Support

- **COMPASS**: Pretrained transformer for single-cell and bulk RNA-seq data

## 🎓 Training Modes

LLaOA supports multiple training strategies:

### 1. Projector-Only Training (Fastest)
```bash
./examples/training/train_projector_only.sh
```
- Trains only the omics projector
- Keeps language model and omics encoder frozen
- Minimal GPU memory requirements (4-8GB)

### 2. Projector + LoRA Training (Balanced)
```bash
./examples/training/train_projector_language_model_lora.sh
```
- Trains projector + LoRA adapters for language model
- Memory efficient fine-tuning
- Good balance of adaptation and efficiency (8-12GB)

### 3. Full Fine-tuning (Maximum Adaptation)
```bash
./examples/training/train_projector_language_model.sh
```
- Trains projector + full language model
- Maximum adaptation capabilities
- High GPU memory requirements (16GB+)

### 4. Encoder + Projector Training
```bash
./examples/training/train_omics_encoder_projector.sh
```
- Trains COMPASS encoder + projector
- Adapts omics understanding to specific datasets
- Medium GPU memory requirements (8-16GB)

## 🧪 Testing

⚠️ **Testing Status**: The testing framework is temporarily disabled during active development. Testing scripts will be rebuilt once core development is completed.

For manual testing during development:
```bash
# Test core component imports
python -c "
import sys; sys.path.append('.')
from llaoa.model.language_model import LlaoaLlamaForCausalLM
from llaoa.model.omics_encoder.compass_encoder import COMPASSOmicsTower
print('✓ Core LLAMA + COMPASS components working')
"

# Test builder functions
python -c "
import sys; sys.path.append('.')
from llaoa.model.builder import get_model_class, identify_model_type
print('✓ Model builder functions working')
"
```

## 📊 Data Processing

```bash
# Process omics data for training
uv run python process_data.py \
    --input_data ./raw_data/rnaseq.csv \
    --output_dir ./data/processed \
    --format h5

# Generate question-answer pairs
uv run python -m llaoa.data.generate_qa \
    --omics_data ./data/processed/rnaseq.h5 \
    --templates ./data/templates/qa_templates.json \
    --output ./data/qa_pairs.json
```

## 💡 Usage Examples

### Training a Custom Model
```bash
uv run python run_train.py \
    --model_path microsoft/DialoGPT-medium \
    --omics_tower_path ./models/compass_model \
    --data_path ./data/my_dataset/qa_pairs.json \
    --omics_data_path ./data/my_dataset/rnaseq.h5 \
    --output_dir ./checkpoints/my_custom_model \
    --num_train_epochs 3 \
    --per_device_train_batch_size 4
```

### Running Evaluation
```bash
uv run python run_eval.py \
    --model_path ./checkpoints/my_custom_model \
    --test_data ./data/test_set.json \
    --output_dir ./evaluation_results
```

## 📁 Project Structure

```
LLaOA/
├── llaoa/                     # Core LLaOA package
│   ├── model/                 # Model architectures
│   │   ├── language_model/    # LLAMA integration
│   │   ├── omics_encoder/     # COMPASS integration  
│   │   └── omics_projector/   # Multimodal projection
│   ├── data/                  # Data processing utilities
│   ├── train/                 # Training scripts
│   └── eval/                  # Evaluation utilities
├── examples/                  # Example training scripts
│   └── training/              # Training examples
├── data/                      # Data storage
├── models/                    # Model storage
├── checkpoints/               # Training outputs
└── docs/                      # Documentation
```

## 📖 Documentation

- **[UV Installation Guide](docs/02a_uv_installation.md)**: Native development setup
- **[Architecture Overview](docs/01_architecture.md)**: System design and components
- **[Training Guide](docs/04_training.md)**: Detailed training instructions
- **[Data Processing](docs/03_data_preparation.md)**: Data formatting and preparation
- **[Testing Guide](docs/06_testing.md)**: Manual testing during development
- **[Customization Guide](docs/07_customization.md)**: Extending LLaOA
- **[API Reference](docs/08_api_reference.md)**: Function and class documentation

## 🛠️ Development

### Prerequisites
- Python 3.9+
- UV package manager
- CUDA-capable GPU (8GB+ VRAM recommended)

### Development Setup
```bash
# Clone repository
git clone https://github.com/your-org/LLaOA.git
cd LLaOA

# Install in development mode
uv sync --dev

# Install pre-commit hooks
uv run pre-commit install
```

### Development Workflow
```bash
# Run code formatting
uv run pre-commit run --all-files

# Manual testing
uv run python -c "from llaoa.model import LlaoaLlamaForCausalLM; print('✓ Import successful')"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests and linting (`uv run pre-commit run --all-files`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **COMPASS**: For providing the foundational omics encoder
- **Meta**: For the LLAMA language model family
- **UV**: For the excellent package management system
- **Transformers**: For the model infrastructure

## 📞 Support

- 📖 Documentation: [docs/](docs/)
- 🐛 Issues: [GitHub Issues](https://github.com/your-org/LLaOA/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/your-org/LLaOA/discussions)
