# TensorBoard Integration for LLaOA

This document describes the zero-configuration TensorBoard integration for LLaOA training.

## Overview

The TensorBoard integration provides:

- **Zero Configuration**: Automatic setup with sensible defaults
- **Real-time Visualization**: Interactive web-based dashboard for monitoring training
- **Comprehensive Metrics**: Training/validation loss, learning rate, system metrics
- **Automatic Directory Structure**: Logs stored in `<output-dir>/<run-name>/logs/tensorboard/`
- **Full Compatibility**: Works seamlessly with existing LLAMA2 and omics workflows

## Usage

### Basic Usage

Enable TensorBoard logging by adding `--report-to tensorboard` to your training command:

```bash
uv run python run_train.py \
    --model-base ./models/llama-2-7b-chat-hf \
    --compass-model-path ./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt \
    --rna-seq-path ./data/dataset_01/rnaseq_tpm.tsv \
    --qa-json-path ./data/dataset_01/qa_pairs.json \
    --output-dir ./checkpoints/my_experiment \
    --run-name my_experiment \
    --report-to tensorboard
```

### Automatic Directory Structure

TensorBoard logs are automatically organized as:

```
outputs/
└── my_experiment/
    ├── checkpoints/           # Model checkpoints
    └── logs/
        └── tensorboard/       # TensorBoard event files
```

### Viewing Results

Start TensorBoard to view the results:

```bash
tensorboard --logdir ./outputs/my_experiment/logs/tensorboard/
# Open browser to http://localhost:6006
```

## Configuration Options

| Argument | Description |
|----------|-------------|
| `--report-to tensorboard` | Enable TensorBoard logging |
| `--report-to none` | Disable TensorBoard logging |
| `--run-name` | Sets the run name (used for directory structure) |

## Metrics Available

### Training Metrics
- `train/loss`: Training loss over time
- `train/learning_rate`: Learning rate schedule
- `train/epoch`: Current epoch
- `train/step`: Training step

### Evaluation Metrics
- `eval/loss`: Validation loss
- `eval/batches_evaluated`: Number of evaluation batches

### System Metrics
- `system/cpu_percent`: CPU utilization
- `system/memory_percent`: Memory usage
- `system/gpu_*_memory_percent`: GPU memory usage per device

### Model Metrics
- `model/total_parameters`: Total model parameters
- `model/trainable_parameters`: Trainable parameters
- `model/trainable_percentage`: Percentage of trainable parameters

## Shell Script Integration

The `train_projector_only.sh` script includes TensorBoard support:

```bash
# Enable TensorBoard
ENABLE_TENSORBOARD=true

# Disable TensorBoard
ENABLE_TENSORBOARD=false
```

## Benefits

1. **Zero Configuration**: No complex setup required
2. **Automatic Organization**: Sensible directory structure
3. **Real-time Monitoring**: Live training visualization
4. **Local Operation**: No external dependencies or internet required
5. **Rich Visualizations**: Interactive plots and metrics

## Troubleshooting

### Common Issues

1. **TensorBoard not available**: Install with `uv add tensorboard`
2. **Port conflicts**: Use `tensorboard --logdir <path> --port 8080`
3. **Permission errors**: Ensure write access to output directory

### Disabling TensorBoard

To disable TensorBoard logging:
```bash
--report-to none
```

## Integration Details

- **Code**: `llaoa/utils/tensorboard_utils.py`
- **Training**: Integrated in `llaoa/train/train.py`
- **CLI**: Configured in `run_train.py`
- **Dependencies**: `tensorboard>=2.10.0` (automatically included)
