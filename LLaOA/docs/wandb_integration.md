# Weights & Biases (wandb) Integration for LLaOA

This document describes the comprehensive Weights & Biases (wandb) integration added to LLaOA training, providing detailed logging and experiment tracking capabilities.

## Overview

The wandb integration provides:

- **Offline Mode**: No internet connectivity required during training
- **Automatic Project Naming**: Project names derived from checkpoint folder names
- **Comprehensive Metrics**: Training/validation loss, learning rate, system metrics
- **Centralized Logs**: All wandb logs stored in `LLaOA/logs` directory
- **Full Compatibility**: Works seamlessly with existing LLAMA2 chat template and omics processing

## Features

### 🔄 Offline Mode
- Runs completely offline without requiring internet connectivity
- Logs stored locally in `LLaOA/logs` directory
- Can be synced to wandb cloud later if desired

### 📊 Comprehensive Metrics Tracking
- **Training Metrics**: Loss, learning rate, epoch, step
- **Validation Metrics**: Evaluation loss, number of batches evaluated
- **System Metrics**: CPU usage, memory usage, GPU memory utilization
- **Model Metrics**: Total parameters, trainable parameters, trainable percentage

### 🎯 Automatic Configuration
- **Project Name**: Auto-derived from checkpoint folder (e.g., `llaoa_projector_training`)
- **Run Name**: Auto-derived from full checkpoint folder name with timestamp
- **Logs Directory**: Automatically set to `LLaOA/logs`

## Usage

### Basic Usage

Enable wandb logging by adding these arguments to your training command:

```bash
uv run python run_train.py \
    # ... your existing arguments ... \
    --report-to wandb \
    --wandb-enabled \
    --wandb-offline
```

### Advanced Configuration

```bash
uv run python run_train.py \
    # ... your existing arguments ... \
    --report-to wandb \
    --wandb-enabled \
    --wandb-offline \
    --wandb-project "my_custom_project" \
    --wandb-run-name "my_custom_run" \
    --wandb-tags "experiment,genomics,llama2" \
    --wandb-notes "Testing new hyperparameters"
```

### Complete Example

See `examples/training/train_with_wandb.sh` for a complete working example.

## Configuration Options

### Command Line Arguments

| Argument | Default | Description |
|----------|---------|-------------|
| `--wandb-enabled` | `True` | Enable wandb logging |
| `--no-wandb` | `False` | Disable wandb logging |
| `--wandb-offline` | `True` | Run in offline mode |
| `--wandb-project` | Auto-derived | Project name |
| `--wandb-run-name` | Auto-derived | Run name |
| `--wandb-logs-dir` | `./logs` | Logs directory |
| `--wandb-tags` | `None` | Comma-separated tags |
| `--wandb-notes` | `None` | Notes for the run |

### Auto-Derived Names

When not specified, project and run names are automatically derived from the output directory:

- **Output Dir**: `./checkpoints/llaoa_projector_training_20250623_123456`
- **Project Name**: `llaoa_projector_training`
- **Run Name**: `llaoa_projector_training_20250623_123456`

## Logged Metrics

### Training Metrics
- `train/loss`: Average training loss
- `train/learning_rate`: Current learning rate
- `train/epoch`: Current epoch
- `train/step`: Current training step

### Validation Metrics
- `eval/loss`: Validation loss
- `eval/epoch`: Epoch when evaluation was performed
- `eval/step`: Step when evaluation was performed
- `eval/batches_evaluated`: Number of batches used for evaluation

### System Metrics
- `system/cpu_percent`: CPU utilization percentage
- `system/memory_percent`: Memory utilization percentage
- `system/gpu_{i}_memory_percent`: GPU memory utilization for each GPU

### Model Metrics
- `model/total_parameters`: Total number of model parameters
- `model/trainable_parameters`: Number of trainable parameters
- `model/trainable_percentage`: Percentage of trainable parameters

## Directory Structure

```
LLaOA/
├── logs/                           # Wandb logs directory
│   ├── cache/                      # Wandb cache
│   ├── config/                     # Wandb config
│   └── wandb/                      # Wandb run data
│       ├── offline-run-*/          # Offline run directories
│       ├── debug.log               # Debug logs
│       └── latest-run              # Symlink to latest run
├── checkpoints/                    # Training checkpoints
│   └── llaoa_projector_training_*/ # Timestamped checkpoint folders
└── ...
```

## Viewing Logs

### Offline Viewing

1. Navigate to the logs directory:
   ```bash
   cd logs
   ```

2. Start wandb offline server:
   ```bash
   wandb offline
   ```

3. Open your browser to the provided URL (usually `http://localhost:8080`)

### Syncing to Cloud (Optional)

If you want to sync offline runs to wandb cloud later:

```bash
cd logs
wandb sync wandb/offline-run-*
```

## Integration Details

### Code Structure

- **`llaoa/train/wandb_utils.py`**: Core wandb utilities and configuration
- **`llaoa/train/train.py`**: Main training loop with wandb integration
- **`run_train.py`**: Command-line interface with wandb arguments

### Key Components

1. **WandbConfig**: Handles configuration and environment setup
2. **initialize_wandb()**: Initializes wandb with proper settings
3. **log_metrics()**: Logs metrics during training
4. **log_model_info()**: Logs model information
5. **finish_wandb()**: Properly closes wandb run

## Compatibility

### Existing Workflows
- ✅ **LLAMA2 Chat Template**: Fully compatible
- ✅ **Omics Data Processing**: No changes required
- ✅ **COMPASS Integration**: Works seamlessly
- ✅ **Distributed Training**: Supports multi-GPU setups
- ✅ **Checkpoint Resuming**: Maintains wandb continuity

### Dependencies
- **wandb**: Already included in `pyproject.toml`
- **psutil**: For system metrics (already included)
- **torch**: For GPU metrics (already included)

## Troubleshooting

### Common Issues

1. **Wandb not available**: The integration gracefully handles missing wandb installation
2. **Timeout in offline mode**: Expected behavior, wandb will still log locally
3. **Permission errors**: Ensure write access to the logs directory

### Debug Information

Enable debug logging by setting:
```bash
export WANDB_DEBUG=true
```

### Disabling Wandb

To completely disable wandb logging:
```bash
uv run python run_train.py \
    # ... your arguments ... \
    --no-wandb
```

Or set:
```bash
--report-to none
```

## Best Practices

1. **Use Descriptive Tags**: Add meaningful tags to categorize experiments
2. **Add Notes**: Include experiment descriptions and hypotheses
3. **Regular Syncing**: Periodically sync offline runs to cloud for backup
4. **Monitor System Metrics**: Use logged system metrics to optimize resource usage
5. **Consistent Naming**: Use consistent project naming conventions

## Examples

### Minimal Example
```bash
uv run python run_train.py \
    --model-base ./models/llama-2-7b-chat-hf \
    --compass-model-path ./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt \
    --rna-seq-path ./data/dataset_01/rnaseq_tpm.tsv \
    --qa-json-path ./data/dataset_01/qa_pairs.json \
    --output-dir ./checkpoints/test_wandb \
    --report-to wandb \
    --wandb-enabled
```

### Full Configuration Example
```bash
uv run python run_train.py \
    --model-base ./models/llama-2-7b-chat-hf \
    --compass-model-path ./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt \
    --rna-seq-path ./data/dataset_01/rnaseq_tpm.tsv \
    --qa-json-path ./data/dataset_01/qa_pairs.json \
    --output-dir ./checkpoints/llaoa_experiment_$(date +%Y%m%d_%H%M%S) \
    --num-train-epochs 3 \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --report-to wandb \
    --wandb-enabled \
    --wandb-offline \
    --wandb-project "llaoa_genomics_experiments" \
    --wandb-tags "projector-only,llama2,genomics,tcga" \
    --wandb-notes "Experimenting with projector-only training on TCGA data"
```

This integration provides comprehensive experiment tracking while maintaining full compatibility with the existing LLaOA training workflow.
