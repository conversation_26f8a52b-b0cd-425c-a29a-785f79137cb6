# LLaOA Documentation

Welcome to the comprehensive documentation for LLaOA (Large Language and Omics Assistant).

## 📖 Table of Contents

1. **[Architecture Overview](01_architecture.md)**: Core system design and architectural components
2. **[UV Installation Guide](02a_uv_installation.md)**: Complete installation guide for development with UV package manager
3. **[Data Preparation](03_data_preparation.md)**: Data formatting, preprocessing, and preparation guidelines
4. **[Training Guide](04_training.md)**: Comprehensive training instructions and strategies
5. **[Evaluation Guide](05_evaluation.md)**: Model evaluation methods and metrics
6. **[Testing Guide](06_testing.md)**: Manual testing procedures during development
7. **[Customization Guide](07_customization.md)**: Extending and customizing LLaOA
8. **[API Reference](08_api_reference.md)**: Detailed API documentation

## 🚀 Quick Start Guide

### Installation with UV (Recommended)

LLaOA is designed to work seamlessly with the UV package manager for fast, reliable development.

```bash
# Install UV (if not already installed)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clone and setup LLaOA
git clone https://github.com/your-org/LLaOA.git
cd LLaOA

# Install dependencies and setup environment
uv sync
```

**See:** [UV Installation Guide](02a_uv_installation.md)

## 🎯 Usage Patterns

| Use Case | Method | Command Example |
|----------|--------|-----------------|
| Development | UV | `uv run python run_train.py --config config.yaml` |
| Training | UV | `./examples/training/train_projector_only.sh` |
| Evaluation | UV | `uv run python run_eval.py --llaoa-checkpoint ./checkpoints/model` |
| Data Processing | UV | `uv run python process_data.py --input data.csv` |

## 🧬 Core Components

- **UV**: Ultra-fast package management and dependency resolution
- **LLAMA**: Primary language model family (7B, 13B, 70B variants)
- **COMPASS**: Omics encoder for RNA-seq and transcriptomic data

## 📝 Documentation Organization

### Installation and Setup
- Start with [UV Installation Guide](02a_uv_installation.md) for development setup
- Follow [Data Preparation](03_data_preparation.md) to format your data

### Training and Evaluation
- Use [Training Guide](04_training.md) for model training strategies
- Apply [Evaluation Guide](05_evaluation.md) for model assessment

### Development and Testing
- Reference [Testing Guide](06_testing.md) for manual testing procedures
- Explore [Customization Guide](07_customization.md) for extending functionality
- Check [API Reference](08_api_reference.md) for detailed function documentation

## 🔧 Development Workflow

1. **Setup**: Install UV and sync dependencies
2. **Data**: Prepare omics data and QA pairs
3. **Train**: Use provided training scripts or custom configurations
4. **Evaluate**: Assess model performance on test data
5. **Deploy**: Use trained models for inference

## 📚 Additional Resources

- **Architecture**: Understanding the technical design
- **Examples**: Sample training and evaluation scripts
- **API**: Detailed function and class documentation
- **Troubleshooting**: Common issues and solutions

## 🤝 Contributing

For contributing guidelines and development practices, see the [Customization Guide](07_customization.md) and individual documentation sections.

---

**Next Steps**: Start with the [UV Installation Guide](02a_uv_installation.md) to set up your development environment.
