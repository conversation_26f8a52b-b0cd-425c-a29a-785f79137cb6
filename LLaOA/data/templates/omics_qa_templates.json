{"cancer_type": [{"question_template": "To which cancer type does this RNAseq sample belong?", "answer_template": "This RNAseq sample belongs to the {cancer_type} cancer type."}, {"question_template": "What is the cancer type of this RNAseq sample?", "answer_template": "The cancer type of this RNAseq sample is {cancer_type}."}, {"question_template": "Can you identify the cancer type for this sample based on its omics profile?", "answer_template": "Based on the omics data, the cancer type is {cancer_type}."}, {"question_template": "Which type of cancer is represented by this sample?", "answer_template": "This sample represents {cancer_type}."}, {"question_template": "Please specify the cancer type for this patient.", "answer_template": "The patient has {cancer_type}."}, {"question_template": "What diagnosis does this RNAseq data suggest?", "answer_template": "The RNAseq data suggests a diagnosis of {cancer_type}."}], "high_expressed_genes": [{"question_template": "What are the highly expressed genes in this RNAseq sample?", "answer_template": "The highly expressed genes in this RNAseq sample are {genes}."}, {"question_template": "List the top overexpressed genes in this sample.", "answer_template": "The top overexpressed genes are {genes}."}, {"question_template": "Which genes show the highest expression levels in this sample?", "answer_template": "The genes with the highest expression levels are {genes}."}, {"question_template": "Can you tell me the most active genes in this RNAseq profile?", "answer_template": "The most active genes in this profile are {genes}."}, {"question_template": "Identify the genes with elevated expression in this sample.", "answer_template": "The genes with elevated expression are {genes}."}, {"question_template": "Which genes are upregulated in this RNAseq data?", "answer_template": "The upregulated genes in this data are {genes}."}], "low_expressed_genes": [{"question_template": "What are the low expressed genes in this RNAseq sample?", "answer_template": "The low expressed genes in this RNAseq sample are {genes}."}, {"question_template": "List the genes with the lowest expression in this sample.", "answer_template": "The genes with the lowest expression are {genes}."}, {"question_template": "Which genes are minimally expressed in this RNAseq profile?", "answer_template": "The minimally expressed genes are {genes}."}, {"question_template": "Can you identify the downregulated genes in this sample?", "answer_template": "The downregulated genes in this sample are {genes}."}, {"question_template": "Which genes show reduced expression in this data?", "answer_template": "The genes with reduced expression are {genes}."}, {"question_template": "What genes are least active in this RNAseq sample?", "answer_template": "The least active genes in this sample are {genes}."}]}