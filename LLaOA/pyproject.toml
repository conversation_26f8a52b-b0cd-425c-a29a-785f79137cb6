[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "llaoa"
version = "0.1.1"
description = "Large Language and Omics Assistant"
readme = "README.md"
requires-python = ">=3.8.1"
license = { text = "Apache 2.0" }
authors = [
    { name = "Bala Suraj Pedasingu", email = "<EMAIL>" }
]
keywords = ["llm", "omics", "rnaseq", "compass", "llava"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # Core ML frameworks (using exact versions from LLaVA as it's more recent)
    "torch==2.1.2",
    "torchvision==0.16.2",
    "torchaudio==2.1.2",
    "transformers==4.37.2",
    "tokenizers==0.15.1",
    "accelerate==0.21.0",
    # Data processing
    "pandas>=1.3.0",
    "numpy",
    "scikit-learn==1.2.2",
    "einops==0.6.1",
    "einops-exts==0.0.4",
    # NLP/Text processing
    "sentencepiece==0.1.99",
    "protobuf>=3.20.0",
    # Visualization and plotting (fixed compatibility issues)
    "matplotlib>=3.6.0,<3.9.0",
    "seaborn>=0.11.0,<0.12.0",
    "colorcet",
    # Scientific computing
    "umap-learn",
    "statannotations==0.6.0",
    # Web/API frameworks
    "requests",
    "httpx==0.24.0",
    "fastapi",
    "uvicorn",
    "pydantic",
    # File processing (added for COMPASS dependencies)
    "openpyxl==3.1.5",
    "plotly==6.1.2",
    "gdown==5.2.0",
    # Utilities
    "shortuuid",
    "markdown2[all]",
    "timm==0.6.13",
    "wandb>=0.20.1",
    "nltk>=3.9.1",
]

[project.optional-dependencies]
# Training dependencies
train = [
    "datasets>=2.10.0",
    "deepspeed==0.12.6",
    "ninja",
    "wandb",
    "peft==0.8.2",  # Fixed compatibility with accelerate==0.21.0
    "bitsandbytes",
    "gradio==4.16.0",
    "gradio_client==0.8.1",
]

# Bioinformatics dependencies
bio = [
    "comut",
    "gseapy",
    "tidepy",
]

# Development dependencies
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0",
]

# Full installation (all optional dependencies)
all = [
    "llaoa[train,bio,dev]"
]

[project.urls]
Homepage = "https://github.com/azu-oncology-rd/LLaOA"
Repository = "https://github.com/azu-oncology-rd/LLaOA"
Documentation = "https://github.com/azu-oncology-rd/LLaOA/docs"
"Bug Tracker" = "https://github.com/azu-oncology-rd/LLaOA/issues"

[project.scripts]
llaoa-train = "llaoa.train.cli:main"
llaoa-eval = "llaoa.eval.cli:main"
llaoa-process = "llaoa.data.cli:main"

[tool.setuptools.packages.find]
include = ["llaoa*"]
exclude = ["tests*", "docs*", "examples*"]

[tool.setuptools.package-data]
llaoa = ["py.typed", "*.json", "*.yaml", "*.yml"]

# uv-specific configuration
[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0",
]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["llaoa"]

# pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests", "llaoa"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=llaoa",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--strict-markers",
    "--disable-warnings",
]

# mypy configuration
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "torch.*",
    "transformers.*",
    "datasets.*",
    "wandb.*",
    "deepspeed.*",
    "peft.*",
    "bitsandbytes.*",
    "gradio.*",
    "comut.*",
    "gseapy.*",
    "tidepy.*",
]
ignore_missing_imports = true 
