#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Process and augment data for LLaOA.

Updated for streamlined LLAMA2-exclusive implementation:
- Removed deprecated prompt_template and answer_prefix functionality
- Added QA content variations instead of format variations
- Added LLAMA2 compatibility validation
- Compatible with streamlined OmicsQADataset that uses LLAMA2 chat templates exclusively
"""

import os
import argparse
import logging
# Removed unused typing imports - they're not needed in this script

from llaoa.data.processing import (
    load_rnaseq_data,
    load_qa_data,
    normalize_rnaseq_data,
    filter_genes,
    augment_rnaseq_data,
    augment_qa_data,
    create_qa_variations,
    validate_qa_pairs_for_llama2,
    save_processed_data
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Process and augment data for LLaOA")

    # Input files
    parser.add_argument("--rna-seq-path", type=str, required=True,
                        help="Path to RNAseq data (TSV/CSV)")
    parser.add_argument("--qa-json-path", type=str, required=True,
                        help="Path to QA pairs JSON")
    parser.add_argument("--sample-id-col", type=str, default=None,
                        help="Column name for sample ID in RNAseq data")

    # Output directory
    parser.add_argument("--output-dir", type=str, default="./processed_data",
                        help="Directory to save processed data")

    # Normalization options
    parser.add_argument("--normalization-method", type=str, default="standard",
                        choices=["standard", "minmax", "robust", "none"],
                        help="Normalization method")
    parser.add_argument("--log-transform", action="store_true",
                        help="Apply log transformation")
    parser.add_argument("--pseudocount", type=float, default=1.0,
                        help="Pseudocount to add before log transformation")

    # Gene filtering options
    parser.add_argument("--min-expression", type=float, default=0.0,
                        help="Minimum mean expression")
    parser.add_argument("--min-variance", type=float, default=0.0,
                        help="Minimum variance")
    parser.add_argument("--max-genes", type=int, default=None,
                        help="Maximum number of genes to keep")

    # Augmentation options
    parser.add_argument("--augment", action="store_true",
                        help="Apply data augmentation")
    parser.add_argument("--augmentation-method", type=str, default="noise",
                        choices=["noise", "dropout", "swap"],
                        help="Augmentation method")
    parser.add_argument("--noise-level", type=float, default=0.01,
                        help="Level of noise/dropout/swap")
    parser.add_argument("--num-augmentations", type=int, default=1,
                        help="Number of augmentations per sample")

    # QA variation options (replaces deprecated prompt templates)
    parser.add_argument("--create-qa-variations", action="store_true",
                        help="Create content variations of QA pairs (replaces deprecated prompt templates)")
    parser.add_argument("--question-variations", type=str, nargs="+",
                        default=["{question}", "Can you help me understand: {question}",
                                "I need to know: {question}", "Please analyze: {question}"],
                        help="Question content variation patterns")
    parser.add_argument("--answer-variations", type=str, nargs="+",
                        default=["{answer}", "Based on the analysis: {answer}",
                                "The data indicates: {answer}", "From the omics profile: {answer}"],
                        help="Answer content variation patterns")

    # Other options
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed")

    return parser.parse_args()

def main():
    args = parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data
    logger.info(f"Loading RNAseq data from {args.rna_seq_path}")
    rnaseq_df = load_rnaseq_data(
        file_path=args.rna_seq_path,
        sample_id_col=args.sample_id_col,
        set_index=True
    )
    logger.info(f"Loaded RNAseq data with shape: {rnaseq_df.shape}")

    logger.info(f"Loading QA data from {args.qa_json_path}")
    qa_pairs = load_qa_data(file_path=args.qa_json_path)
    logger.info(f"Loaded {len(qa_pairs)} QA pairs")

    # Normalize RNAseq data
    if args.normalization_method != "none":
        logger.info(f"Normalizing RNAseq data with method: {args.normalization_method}")
        rnaseq_df = normalize_rnaseq_data(
            df=rnaseq_df,
            method=args.normalization_method,
            log_transform=args.log_transform,
            pseudocount=args.pseudocount
        )

    # Filter genes
    if args.min_expression > 0.0 or args.min_variance > 0.0 or args.max_genes is not None:
        logger.info("Filtering genes")
        rnaseq_df = filter_genes(
            df=rnaseq_df,
            min_expression=args.min_expression,
            min_variance=args.min_variance,
            max_genes=args.max_genes
        )
        logger.info(f"After filtering, RNAseq data shape: {rnaseq_df.shape}")

    # Augment data
    if args.augment:
        logger.info(f"Augmenting RNAseq data with method: {args.augmentation_method}")
        rnaseq_df_aug, aug_sample_ids = augment_rnaseq_data(
            df=rnaseq_df,
            method=args.augmentation_method,
            noise_level=args.noise_level,
            num_augmentations=args.num_augmentations,
            seed=args.seed
        )
        logger.info(f"After augmentation, RNAseq data shape: {rnaseq_df_aug.shape}")

        # Create mapping from original to augmented sample IDs
        original_to_augmented = {}
        for aug_id in aug_sample_ids:
            original_id = aug_id.split("_aug")[0]
            if original_id not in original_to_augmented:
                original_to_augmented[original_id] = []
            original_to_augmented[original_id].append(aug_id)

        # Augment QA data
        logger.info("Augmenting QA data")
        qa_pairs_aug = augment_qa_data(
            qa_pairs=qa_pairs,
            original_to_augmented=original_to_augmented
        )
        logger.info(f"After augmentation, number of QA pairs: {len(qa_pairs_aug)}")

        # Update variables
        rnaseq_df = rnaseq_df_aug
        qa_pairs = qa_pairs_aug

    # Create QA variations (replaces deprecated prompt templates)
    if args.create_qa_variations:
        logger.info("Creating QA content variations")
        qa_pairs = create_qa_variations(
            qa_pairs=qa_pairs,
            question_variations=args.question_variations,
            answer_variations=args.answer_variations,
            seed=args.seed
        )
        logger.info(f"Created variations, total QA pairs: {len(qa_pairs)}")

    # Validate and clean QA pairs for LLAMA2 compatibility
    logger.info("Validating QA pairs for LLAMA2 compatibility")
    qa_pairs = validate_qa_pairs_for_llama2(qa_pairs)
    logger.info("QA pairs validated and cleaned for streamlined LLaOA dataset")

    # Save processed data
    logger.info(f"Saving processed data to {args.output_dir}")
    rnaseq_path, qa_path = save_processed_data(
        rnaseq_df=rnaseq_df,
        qa_pairs=qa_pairs,
        output_dir=args.output_dir
    )
    logger.info(f"Saved RNAseq data to {rnaseq_path}")
    logger.info(f"Saved Question-Answer pairs data to {qa_path}")

if __name__ == "__main__":
    main()
