"""
Visualization utilities for evaluation results.
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Union, Any, Tuple
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
from wordcloud import WordCloud
from collections import Counter

def plot_metrics_comparison(
    results_paths: List[str],
    output_dir: str,
    metrics: Optional[List[str]] = None,
    model_names: Optional[List[str]] = None,
    title: str = "Model Comparison"
) -> None:
    """
    Plot a comparison of metrics across different models.

    Args:
        results_paths: List of paths to results.json files
        output_dir: Directory to save the plots
        metrics: List of metrics to compare (if None, use all common metrics)
        model_names: List of model names (if None, use filenames)
        title: Title for the plot
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Load results
    results = []
    names = []
    for i, path in enumerate(results_paths):
        with open(path, 'r') as f:
            result = json.load(f)
            results.append(result)

        # Use provided model name or extract from path
        if model_names and i < len(model_names):
            names.append(model_names[i])
        else:
            # Extract model name from path
            name = os.path.basename(os.path.dirname(path))
            names.append(name)

    # Find common metrics if not specified
    if metrics is None:
        # Get all metrics from all results
        all_metrics = set()
        for result in results:
            all_metrics.update(result.keys())

        # Filter to metrics present in all results
        metrics = []
        for metric in all_metrics:
            if all(metric in result for result in results):
                metrics.append(metric)

    # Create DataFrame for plotting
    data = []
    for i, result in enumerate(results):
        for metric in metrics:
            if metric in result:
                data.append({
                    'Model': names[i],
                    'Metric': metric,
                    'Value': result[metric]
                })

    df = pd.DataFrame(data)

    # Plot
    plt.figure(figsize=(12, 8))
    sns.barplot(x='Metric', y='Value', hue='Model', data=df)
    plt.title(title)
    plt.xticks(rotation=45)
    plt.tight_layout()

    # Save plot
    plt.savefig(os.path.join(output_dir, 'metrics_comparison.png'), dpi=300)
    plt.close()

def plot_confusion_matrix(
    true_labels: List[int],
    pred_labels: List[int],
    output_dir: str,
    class_names: Optional[List[str]] = None,
    title: str = "Confusion Matrix"
) -> None:
    """
    Plot a confusion matrix for classification results.

    Args:
        true_labels: List of true labels
        pred_labels: List of predicted labels
        output_dir: Directory to save the plot
        class_names: List of class names (if None, use label indices)
        title: Title for the plot
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Compute confusion matrix
    cm = confusion_matrix(true_labels, pred_labels)

    # Plot
    plt.figure(figsize=(10, 8))
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap=plt.cm.Blues)
    plt.title(title)
    plt.tight_layout()

    # Save plot
    plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'), dpi=300)
    plt.close()

def plot_generation_length_distribution(
    generated_texts: List[str],
    reference_texts: List[str],
    output_dir: str,
    title: str = "Text Length Distribution"
) -> None:
    """
    Plot the distribution of text lengths for generated and reference texts.

    Args:
        generated_texts: List of generated texts
        reference_texts: List of reference texts
        output_dir: Directory to save the plot
        title: Title for the plot
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Compute text lengths
    gen_lengths = [len(text.split()) for text in generated_texts]
    ref_lengths = [len(text.split()) for text in reference_texts]

    # Plot
    plt.figure(figsize=(10, 6))
    plt.hist(gen_lengths, alpha=0.5, label='Generated', bins=20)
    plt.hist(ref_lengths, alpha=0.5, label='Reference', bins=20)
    plt.xlabel('Text Length (words)')
    plt.ylabel('Frequency')
    plt.title(title)
    plt.legend()
    plt.tight_layout()

    # Save plot
    plt.savefig(os.path.join(output_dir, 'text_length_distribution.png'), dpi=300)
    plt.close()

def generate_word_clouds(
    generated_texts: List[str],
    reference_texts: List[str],
    output_dir: str,
    title: str = "Word Clouds"
) -> None:
    """
    Generate word clouds for generated and reference texts.

    Args:
        generated_texts: List of generated texts
        reference_texts: List of reference texts
        output_dir: Directory to save the plots
        title: Title for the plots
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Combine texts
    gen_text = ' '.join(generated_texts)
    ref_text = ' '.join(reference_texts)

    # Generate word clouds
    gen_cloud = WordCloud(width=800, height=400, background_color='white').generate(gen_text)
    ref_cloud = WordCloud(width=800, height=400, background_color='white').generate(ref_text)

    # Plot generated text word cloud
    plt.figure(figsize=(10, 5))
    plt.imshow(gen_cloud, interpolation='bilinear')
    plt.axis('off')
    plt.title(f"{title} - Generated")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'wordcloud_generated.png'), dpi=300)
    plt.close()

    # Plot reference text word cloud
    plt.figure(figsize=(10, 5))
    plt.imshow(ref_cloud, interpolation='bilinear')
    plt.axis('off')
    plt.title(f"{title} - Reference")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'wordcloud_reference.png'), dpi=300)
    plt.close()

def plot_metrics_by_sample(
    metrics_by_sample: Dict[str, List[float]],
    sample_ids: List[str],
    output_dir: str,
    title: str = "Metrics by Sample"
) -> None:
    """
    Plot metrics for each sample.

    Args:
        metrics_by_sample: Dictionary mapping metric names to lists of values for each sample
        sample_ids: List of sample IDs
        output_dir: Directory to save the plots
        title: Title for the plots
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create DataFrame
    data = {metric: values for metric, values in metrics_by_sample.items()}
    data['Sample ID'] = sample_ids
    df = pd.DataFrame(data)

    # Plot each metric
    for metric in metrics_by_sample.keys():
        plt.figure(figsize=(12, 6))
        sns.barplot(x='Sample ID', y=metric, data=df)
        plt.title(f"{title} - {metric}")
        plt.xticks(rotation=90)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'metric_by_sample_{metric}.png'), dpi=300)
        plt.close()

def create_html_report(
    results: Dict[str, float],
    generated_texts: List[str],
    reference_texts: List[str],
    questions: List[str],
    sample_ids: List[str],
    output_dir: str,
    model_name: str = "LLaOA Model"
) -> None:
    """
    Create an HTML report of evaluation results.

    Args:
        results: Dictionary of evaluation metrics
        generated_texts: List of generated texts
        reference_texts: List of reference texts
        questions: List of questions
        sample_ids: List of sample IDs
        output_dir: Directory to save the report
        model_name: Name of the model
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create HTML content
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Evaluation Report - {model_name}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #333; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .metrics {{ display: flex; flex-wrap: wrap; }}
            .metric {{ margin: 10px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; width: 200px; }}
            .metric h3 {{ margin-top: 0; }}
            .sample {{ margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
            .sample h3 {{ margin-top: 0; }}
            .generated {{ background-color: #e6f7ff; padding: 10px; border-radius: 5px; }}
            .reference {{ background-color: #f0f5e6; padding: 10px; border-radius: 5px; }}
        </style>
    </head>
    <body>
        <h1>Evaluation Report - {model_name}</h1>

        <h2>Metrics Summary</h2>
        <div class="metrics">
    """

    # Add metrics
    for metric, value in results.items():
        html += f"""
            <div class="metric">
                <h3>{metric}</h3>
                <p>{value:.4f}</p>
            </div>
        """

    html += """
        </div>

        <h2>Sample Results</h2>
    """

    # Add samples
    for i, (sample_id, question, gen, ref) in enumerate(zip(sample_ids, questions, generated_texts, reference_texts)):
        html += f"""
        <div class="sample">
            <h3>Sample {i+1}: {sample_id}</h3>
            <p><strong>Question:</strong> {question}</p>
            <div class="generated">
                <p><strong>Generated:</strong> {gen}</p>
            </div>
            <div class="reference">
                <p><strong>Reference:</strong> {ref}</p>
            </div>
        </div>
        """

    html += """
    </body>
    </html>
    """

    # Write HTML to file
    with open(os.path.join(output_dir, 'report.html'), 'w') as f:
        f.write(html)
