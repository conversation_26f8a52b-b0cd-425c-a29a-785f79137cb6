import os
import torch
import argparse
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from torch.utils.data import DataLoader
from transformers import AutoTokenizer, HfArgumentParser
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
from accelerate import Accelerator
from accelerate.logging import get_logger
from accelerate.utils import set_seed

from llaoa.model.builder import load_pretrained_model
from llaoa.data.omics_qa_dataset import OmicsQADataset

# Set up logging - defer accelerate logger until after initialization
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import nltk for BLEU score
try:
    import nltk
    nltk.download('punkt', quiet=True)
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False
    logger.warning("NLTK not installed, BLEU score will not be available")

# Try to import rouge for ROUGE score
try:
    from rouge_score import rouge_scorer
    ROUGE_AVAILABLE = True
except ImportError:
    ROUGE_AVAILABLE = False
    logger.warning("rouge_score not installed, ROUGE score will not be available")

def worker_init_fn(worker_id):
    """
    Worker initialization function for DataLoader to ensure proper seeding across workers.
    
    Args:
        worker_id: Worker process ID
    """
    # Get the initial seed and make it specific to each worker
    worker_seed = torch.initial_seed() % 2**32
    np.random.seed(worker_seed)
    import random
    random.seed(worker_seed)

@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to evaluate.
    Supports both complete LLaOA models and training checkpoints.
    """
    model_path: str
    is_checkpoint: bool = False  # Whether model_path is a training checkpoint directory

@dataclass
class DataArguments:
    """
    Arguments pertaining to what data we are going to input our model for evaluation.
    """
    rna_seq_path: str = None  # Will be resolved at runtime
    qa_json_path: str = None  # Will be resolved at runtime
    sample_id_col: Optional[str] = None
    max_length: int = 512
    # Note: seed is provided by EvalArguments, not here

    def __post_init__(self):
        # Resolve RNA-seq path if not explicitly provided
        if self.rna_seq_path is None:
            from pathlib import Path
            # Try multiple possible locations
            possible_paths = [
                "COMPASS/example/data/compass_gide_tpm.tsv",
                "../COMPASS/example/data/compass_gide_tpm.tsv",
                str(Path(__file__).parents[2] / "COMPASS/example/data/compass_gide_tpm.tsv"),
                str(Path(__file__).parents[3] / "COMPASS/example/data/compass_gide_tpm.tsv")
            ]
            for path in possible_paths:
                if Path(path).exists():
                    self.rna_seq_path = path
                    break
            if self.rna_seq_path is None:
                self.rna_seq_path = "COMPASS/example/data/compass_gide_tpm.tsv"  # Default fallback

        # Resolve QA JSON path if not explicitly provided
        if self.qa_json_path is None:
            from pathlib import Path
            # Try multiple possible locations
            possible_paths = [
                "data/omics_qa.json",
                "../data/omics_qa.json",
                str(Path(__file__).parents[2] / "data/omics_qa.json"),
                str(Path(__file__).parents[3] / "data/omics_qa.json")
            ]
            for path in possible_paths:
                if Path(path).exists():
                    self.qa_json_path = path
                    break
            if self.qa_json_path is None:
                self.qa_json_path = "data/omics_qa.json"  # Default fallback

@dataclass
class EvalArguments:
    """
    Arguments pertaining to evaluation.
    """
    output_dir: str = "./eval_results"
    per_device_eval_batch_size: int = 4
    generation_mode: bool = False
    max_new_tokens: int = 128
    num_beams: int = 1
    do_sample: bool = False
    temperature: float = 1.0
    top_p: float = 1.0
    top_k: int = 50
    repetition_penalty: float = 1.0
    length_penalty: float = 1.0
    no_repeat_ngram_size: int = 0
    fp16: bool = False
    bf16: bool = False
    dataloader_num_workers: int = 4
    seed: int = 42

def omics_collate_fn(batch, tokenizer=None):
    """
    Collate function for batching omics data and QA pairs.
    Compatible with both legacy and streamlined dataset formats.

    Args:
        batch: List of dictionaries with 'omics' and either legacy or streamlined fields
        tokenizer: Optional tokenizer for getting pad_token_id

    Returns:
        Dictionary with batched data
    """
    # Stack omics tensors into a batch tensor
    # Each item['omics'] has shape [1, 1, num_genes]
    omics_tensors = [item['omics'] for item in batch]
    omics_batch = torch.cat(omics_tensors, dim=0)  # Shape: [batch_size, 1, num_genes]

    # Get pad token ID
    pad_token_id = getattr(tokenizer, 'pad_token_id', 0) if tokenizer is not None else 0

    # Include original questions and answers for evaluation
    questions = [item['question'] for item in batch]
    answers = [item['answer'] for item in batch]
    sample_ids = [item['sample_id'] for item in batch]

    result = {
        'omics': omics_batch,  # Tensor with shape [batch_size, 1, num_genes]
        'questions': questions,
        'answers': answers,
        'sample_ids': sample_ids
    }

    # Handle both legacy and streamlined dataset formats
    if 'question_ids' in batch[0] and 'answer_ids' in batch[0]:
        # Legacy format - separate question and answer IDs
        question_ids = torch.nn.utils.rnn.pad_sequence(
            [item['question_ids'] for item in batch],
            batch_first=True,
            padding_value=pad_token_id
        )
        answer_ids = torch.nn.utils.rnn.pad_sequence(
            [item['answer_ids'] for item in batch],
            batch_first=True,
            padding_value=pad_token_id
        )
        result.update({
            'question_ids': question_ids,
            'answer_ids': answer_ids
        })

    if 'input_ids' in batch[0]:
        # Streamlined format - combined input_ids and labels
        input_ids = torch.nn.utils.rnn.pad_sequence(
            [item['input_ids'] for item in batch],
            batch_first=True,
            padding_value=pad_token_id
        )
        labels = torch.nn.utils.rnn.pad_sequence(
            [item['labels'] for item in batch],
            batch_first=True,
            padding_value=-100  # Standard ignore index for labels
        )
        result.update({
            'input_ids': input_ids,
            'labels': labels
        })

    return result

def compute_metrics(
    generated_texts: List[str],
    reference_texts: List[str],
    questions: List[str],
    sample_ids: List[str],
    output_dir: str,
    model_name: str = "LLaOA Model",
    create_visualizations: bool = True
) -> Dict[str, float]:
    """
    Compute evaluation metrics for generated text and create visualizations.

    Args:
        generated_texts: List of generated texts
        reference_texts: List of reference texts
        questions: List of questions
        sample_ids: List of sample IDs
        output_dir: Directory to save visualizations
        model_name: Name of the model
        create_visualizations: Whether to create visualizations

    Returns:
        Dictionary of metrics
    """
    from .metrics import compute_metrics_for_batch

    # Compute metrics
    results = compute_metrics_for_batch(
        predictions=generated_texts,
        references=reference_texts,
        include_per_sample=True
    )

    # Extract per-sample metrics
    per_sample_metrics = {}
    for key in list(results.keys()):
        if key.endswith('_per_sample'):
            base_key = key[:-11]  # Remove '_per_sample'
            per_sample_metrics[base_key] = results.pop(key)

    # Create visualizations if requested
    if create_visualizations:
        try:
            from .visualization import (
                plot_generation_length_distribution,
                generate_word_clouds,
                plot_metrics_by_sample,
                create_html_report
            )

            # Create visualizations directory
            viz_dir = os.path.join(output_dir, 'visualizations')
            os.makedirs(viz_dir, exist_ok=True)

            # Plot text length distribution
            plot_generation_length_distribution(
                generated_texts=generated_texts,
                reference_texts=reference_texts,
                output_dir=viz_dir,
                title=f"Text Length Distribution - {model_name}"
            )

            # Generate word clouds
            generate_word_clouds(
                generated_texts=generated_texts,
                reference_texts=reference_texts,
                output_dir=viz_dir,
                title=f"Word Clouds - {model_name}"
            )

            # Plot metrics by sample
            plot_metrics_by_sample(
                metrics_by_sample=per_sample_metrics,
                sample_ids=sample_ids,
                output_dir=viz_dir,
                title=f"Metrics by Sample - {model_name}"
            )

            # Create HTML report
            create_html_report(
                results=results,
                generated_texts=generated_texts,
                reference_texts=reference_texts,
                questions=questions,
                sample_ids=sample_ids,
                output_dir=output_dir,
                model_name=model_name
            )
        except ImportError as e:
            logger.warning(f"Could not create visualizations: {str(e)}")

    return results

def evaluate():
    """Main evaluation function."""
    # Parse arguments
    parser = HfArgumentParser((ModelArguments, DataArguments, EvalArguments))
    model_args, data_args, eval_args = parser.parse_args_into_dataclasses()

    # Set up accelerator
    accelerator = Accelerator(
        mixed_precision=eval_args.fp16 and "fp16" or eval_args.bf16 and "bf16" or "no"
    )

    # Now we can use accelerate logger
    global logger
    logger = get_logger(__name__)

    # Set seed for reproducibility
    set_seed(eval_args.seed)

    # Create output directory if it doesn't exist
    if accelerator.is_main_process:
        os.makedirs(eval_args.output_dir, exist_ok=True)

    # Load model and tokenizer
    logger.info(f"Loading model from {model_args.model_path}")

    if model_args.is_checkpoint:
        # Load from training checkpoint
        logger.info("Loading from training checkpoint...")

        # Check if this is an accelerate checkpoint directory
        if os.path.exists(os.path.join(model_args.model_path, "pytorch_model.bin")) or \
           os.path.exists(os.path.join(model_args.model_path, "model.safetensors")):
            # This is a saved model directory
            tokenizer, model, scaler, _ = load_pretrained_model(
                llaoa_model_path=model_args.model_path,
                seed=eval_args.seed
            )
        else:
            # This might be an accelerate checkpoint - need to find the base model
            # Look for a config or model files in parent directories
            checkpoint_dir = model_args.model_path
            base_model_path = None

            # Try to find the original model path from training args
            possible_base_paths = [
                os.path.join(os.path.dirname(checkpoint_dir), "final"),
                os.path.dirname(checkpoint_dir),
                "meta-llama/Llama-2-7b-chat-hf"  # Default fallback
            ]

            for path in possible_base_paths:
                if os.path.exists(path) and (
                    os.path.exists(os.path.join(path, "config.json")) or
                    os.path.exists(os.path.join(path, "pytorch_model.bin"))
                ):
                    base_model_path = path
                    break

            if base_model_path is None:
                raise ValueError(f"Could not find base model for checkpoint {model_args.model_path}")

            logger.info(f"Loading base model from {base_model_path}")
            tokenizer, model, scaler, _ = load_pretrained_model(
                llaoa_model_path=base_model_path,
                seed=eval_args.seed
            )

            # Load checkpoint state
            logger.info(f"Loading checkpoint state from {checkpoint_dir}")
            accelerator.load_state(checkpoint_dir)
    else:
        # Load complete model
        tokenizer, model, scaler, _ = load_pretrained_model(
            llaoa_model_path=model_args.model_path,
            seed=eval_args.seed
        )

    # Validate that we have a scaler
    if scaler is None:
        logger.warning("No scaler found - data preprocessing may not work correctly")

    # Load dataset
    logger.info(f"Loading dataset from {data_args.qa_json_path} and {data_args.rna_seq_path}")
    dataset = OmicsQADataset(
        rna_seq_path=data_args.rna_seq_path,
        qa_json_path=data_args.qa_json_path,
        tokenizer=tokenizer,
        max_length=data_args.max_length,
        sample_id_col=data_args.sample_id_col,
        scaler=scaler  # Pass scaler to dataset for preprocessing
    )

    # Create dataloader with tokenizer-aware collate function
    from functools import partial
    collate_fn_with_tokenizer = partial(omics_collate_fn, tokenizer=tokenizer)

    dataloader = DataLoader(
        dataset,
        batch_size=eval_args.per_device_eval_batch_size,
        shuffle=False,
        collate_fn=collate_fn_with_tokenizer,
        num_workers=eval_args.dataloader_num_workers,
        worker_init_fn=worker_init_fn
    )

    # Prepare model and dataloader with accelerator
    model, dataloader = accelerator.prepare(model, dataloader)

    # Set model to evaluation mode
    model.eval()

    # Prepare for evaluation
    all_predictions = []
    all_labels = []
    all_generated_texts = []
    all_reference_texts = []
    all_questions = []
    all_sample_ids = []

    # Log info
    logger.info("***** Running evaluation *****")
    logger.info(f"  Num examples = {len(dataset)}")
    logger.info(f"  Batch size per device = {eval_args.per_device_eval_batch_size}")
    logger.info(f"  Generation mode = {eval_args.generation_mode}")

    # Evaluate
    with torch.no_grad():
        for batch in dataloader:
            omics = batch['omics']  # Tensor
            questions = batch['questions']
            answers = batch['answers']
            sample_ids = batch['sample_ids']

            if eval_args.generation_mode:
                # Determine input format
                if 'question_ids' in batch:
                    # Legacy format
                    input_ids = batch['question_ids']
                elif 'input_ids' in batch:
                    # Streamlined format - extract question part for generation
                    # For generation, we need just the question part
                    # This is a simplified approach - in practice, you might want to
                    # extract the question tokens more carefully
                    input_ids = batch['input_ids']
                else:
                    raise ValueError("No valid input format found in batch")

                # Generate text
                generation_kwargs = {
                    "max_new_tokens": eval_args.max_new_tokens,
                    "do_sample": eval_args.do_sample,
                    "num_beams": eval_args.num_beams,
                    "temperature": eval_args.temperature,
                    "top_p": eval_args.top_p,
                    "top_k": eval_args.top_k,
                    "repetition_penalty": eval_args.repetition_penalty,
                    "length_penalty": eval_args.length_penalty,
                    "no_repeat_ngram_size": eval_args.no_repeat_ngram_size,
                }

                generated_ids = model.generate(
                    inputs=input_ids,
                    omics_data=omics,
                    **generation_kwargs
                )

                # Decode generated text and reference text
                generated_texts = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)
                reference_texts = answers

                all_generated_texts.extend(generated_texts)
                all_reference_texts.extend(reference_texts)
                all_questions.extend(questions)
                all_sample_ids.extend(sample_ids)
            else:
                # Forward pass for loss calculation
                if 'input_ids' in batch and 'labels' in batch:
                    # Streamlined format
                    outputs = model(
                        input_ids=batch['input_ids'],
                        omics_data=omics,
                        labels=batch['labels']
                    )
                elif 'question_ids' in batch and 'answer_ids' in batch:
                    # Legacy format
                    outputs = model(
                        input_ids=batch['question_ids'],
                        omics_data=omics,
                        labels=batch['answer_ids']
                    )
                else:
                    raise ValueError("No valid input format found in batch")

                # For classification metrics, we would need to implement token-level accuracy
                # For now, we'll just compute loss
                if hasattr(outputs, 'loss') and outputs.loss is not None:
                    # Store loss for reporting
                    pass

    # Compute metrics
    results = {}

    if eval_args.generation_mode:
        # For generation mode, compute text-based metrics
        results = compute_metrics(
            generated_texts=all_generated_texts,
            reference_texts=all_reference_texts,
            questions=all_questions,
            sample_ids=all_sample_ids,
            output_dir=eval_args.output_dir,
            model_name=os.path.basename(model_args.model_path),
            create_visualizations=True
        )

        # Save generated texts
        if accelerator.is_main_process:
            with open(os.path.join(eval_args.output_dir, 'generated_texts.txt'), 'w') as f:
                for sample_id, question, gen, ref in zip(all_sample_ids, all_questions, all_generated_texts, all_reference_texts):
                    f.write(f"Sample ID: {sample_id}\n")
                    f.write(f"Question: {question}\n")
                    f.write(f"Generated: {gen}\n")
                    f.write(f"Reference: {ref}\n")
                    f.write("-" * 80 + "\n")

            # Save as CSV for easier analysis
            import csv
            with open(os.path.join(eval_args.output_dir, 'generated_texts.csv'), 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['sample_id', 'question', 'generated', 'reference'])
                for sample_id, question, gen, ref in zip(all_sample_ids, all_questions, all_generated_texts, all_reference_texts):
                    writer.writerow([sample_id, question, gen, ref])
    else:
        # For classification mode, compute standard metrics
        results['accuracy'] = accuracy_score(all_labels, all_predictions)
        results['f1'] = f1_score(all_labels, all_predictions, average='macro')
        results['precision'] = precision_score(all_labels, all_predictions, average='macro')
        results['recall'] = recall_score(all_labels, all_predictions, average='macro')

        # Create confusion matrix if possible
        if accelerator.is_main_process:
            try:
                from .visualization import plot_confusion_matrix

                # Create visualizations directory
                viz_dir = os.path.join(eval_args.output_dir, 'visualizations')
                os.makedirs(viz_dir, exist_ok=True)

                # Plot confusion matrix
                plot_confusion_matrix(
                    true_labels=all_labels,
                    pred_labels=all_predictions,
                    output_dir=viz_dir,
                    title=f"Confusion Matrix - {os.path.basename(model_args.model_path)}"
                )
            except ImportError as e:
                logger.warning(f"Could not create confusion matrix: {str(e)}")

    # Print results
    logger.info("Evaluation Results:")
    for metric, value in results.items():
        logger.info(f"{metric}: {value:.4f}")

    # Save results
    if accelerator.is_main_process:
        with open(os.path.join(eval_args.output_dir, 'results.txt'), 'w') as f:
            for metric, value in results.items():
                f.write(f"{metric}: {value:.4f}\n")

        # Save as JSON for easier parsing
        import json
        with open(os.path.join(eval_args.output_dir, 'results.json'), 'w') as f:
            json.dump({k: float(v) for k, v in results.items()}, f, indent=2)

        # Create comparison plot if previous results exist
        try:
            from .visualization import plot_metrics_comparison

            # Find previous results
            results_paths = []
            for root, _, files in os.walk(os.path.dirname(eval_args.output_dir)):
                for file in files:
                    if file == 'results.json':
                        results_paths.append(os.path.join(root, file))

            # If we have multiple results, create comparison plot
            if len(results_paths) > 1:
                plot_metrics_comparison(
                    results_paths=results_paths,
                    output_dir=os.path.join(eval_args.output_dir, 'visualizations'),
                    title="Model Comparison"
                )
        except ImportError as e:
            logger.warning(f"Could not create comparison plot: {str(e)}")

    return results

if __name__ == '__main__':
    evaluate()