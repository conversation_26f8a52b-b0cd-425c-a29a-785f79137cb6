"""
Advanced evaluation metrics for LLaOA.
"""

import re
import string
import numpy as np
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
from collections import Counter

# Try to import nltk for BLEU score
try:
    import nltk
    from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
    from nltk.tokenize import word_tokenize
    nltk.download('punkt', quiet=True)
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

# Try to import rouge for ROUGE score
try:
    from rouge_score import rouge_scorer
    ROUGE_AVAILABLE = True
except ImportError:
    ROUGE_AVAILABLE = False

# Try to import bert-score
try:
    import torch
    from bert_score import score as bert_score
    BERT_SCORE_AVAILABLE = True
except ImportError:
    BERT_SCORE_AVAILABLE = False

def normalize_text(text: str) -> str:
    """
    Normalize text by removing punctuation, extra whitespace, and converting to lowercase.

    Args:
        text: Input text

    Returns:
        Normalized text
    """
    # Convert to lowercase
    text = text.lower()

    # Remove punctuation
    translator = str.maketrans('', '', string.punctuation)
    text = text.translate(translator)

    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    return text

def exact_match(prediction: str, reference: str, normalize: bool = True) -> float:
    """
    Compute exact match score.

    Args:
        prediction: Predicted text
        reference: Reference text
        normalize: Whether to normalize texts before comparison

    Returns:
        1.0 if exact match, 0.0 otherwise
    """
    if normalize:
        prediction = normalize_text(prediction)
        reference = normalize_text(reference)

    return float(prediction == reference)

def token_match(prediction: str, reference: str, normalize: bool = True) -> float:
    """
    Compute token match score (Jaccard similarity).

    Args:
        prediction: Predicted text
        reference: Reference text
        normalize: Whether to normalize texts before comparison

    Returns:
        Jaccard similarity between prediction and reference tokens
    """
    if normalize:
        prediction = normalize_text(prediction)
        reference = normalize_text(reference)

    # Tokenize
    pred_tokens = set(prediction.split())
    ref_tokens = set(reference.split())

    # Compute Jaccard similarity
    if not pred_tokens and not ref_tokens:
        return 1.0

    intersection = len(pred_tokens.intersection(ref_tokens))
    union = len(pred_tokens.union(ref_tokens))

    return intersection / union

def bleu_score(prediction: str, reference: str, n: int = 4) -> float:
    """
    Compute BLEU score.

    Args:
        prediction: Predicted text
        reference: Reference text
        n: Maximum n-gram order

    Returns:
        BLEU score
    """
    if not NLTK_AVAILABLE:
        raise ImportError("NLTK is required for BLEU score calculation")

    # Tokenize
    pred_tokens = word_tokenize(prediction.lower())
    ref_tokens = [word_tokenize(reference.lower())]

    # Skip empty sequences
    if not pred_tokens or not ref_tokens[0]:
        return 0.0

    # Compute BLEU score
    smoothing = SmoothingFunction().method1
    weights = tuple(1 / n for _ in range(n))

    return sentence_bleu(ref_tokens, pred_tokens, weights=weights, smoothing_function=smoothing)

def rouge_scores(prediction: str, reference: str) -> Dict[str, float]:
    """
    Compute ROUGE scores.

    Args:
        prediction: Predicted text
        reference: Reference text

    Returns:
        Dictionary of ROUGE scores
    """
    if not ROUGE_AVAILABLE:
        raise ImportError("rouge_score is required for ROUGE score calculation")

    # Initialize scorer
    scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)

    # Compute scores
    scores = scorer.score(reference, prediction)

    # Extract F1 scores
    return {
        'rouge1': scores['rouge1'].fmeasure,
        'rouge2': scores['rouge2'].fmeasure,
        'rougeL': scores['rougeL'].fmeasure
    }

def bert_scores(predictions: List[str], references: List[str]) -> Dict[str, List[float]]:
    """
    Compute BERTScore.

    Args:
        predictions: List of predicted texts
        references: List of reference texts

    Returns:
        Dictionary of BERTScore precision, recall, and F1 scores
    """
    if not BERT_SCORE_AVAILABLE:
        raise ImportError("bert_score is required for BERTScore calculation")

    # Compute scores
    P, R, F1 = bert_score(predictions, references, lang='en', return_hash=False)

    # Convert to lists
    return {
        'bert_precision': P.tolist(),
        'bert_recall': R.tolist(),
        'bert_f1': F1.tolist()
    }

def compute_metrics_for_sample(
    prediction: str,
    reference: str,
    metrics: Optional[List[str]] = None
) -> Dict[str, float]:
    """
    Compute multiple metrics for a single sample.

    Args:
        prediction: Predicted text
        reference: Reference text
        metrics: List of metrics to compute (if None, compute all available metrics)

    Returns:
        Dictionary of metric scores
    """
    available_metrics = {
        'exact_match': lambda p, r: exact_match(p, r),
        'token_match': lambda p, r: token_match(p, r),
    }

    if NLTK_AVAILABLE:
        available_metrics['bleu'] = lambda p, r: bleu_score(p, r)

    if ROUGE_AVAILABLE:
        rouge = lambda p, r: rouge_scores(p, r)
        available_metrics['rouge1'] = lambda p, r: rouge(p, r)['rouge1']
        available_metrics['rouge2'] = lambda p, r: rouge(p, r)['rouge2']
        available_metrics['rougeL'] = lambda p, r: rouge(p, r)['rougeL']

    # Use specified metrics or all available
    if metrics is None:
        metrics = list(available_metrics.keys())

    # Compute metrics
    results = {}
    for metric in metrics:
        if metric in available_metrics:
            results[metric] = available_metrics[metric](prediction, reference)

    return results

def compute_metrics_for_batch(
    predictions: List[str],
    references: List[str],
    metrics: Optional[List[str]] = None,
    include_per_sample: bool = False
) -> Dict[str, Union[float, List[float]]]:
    """
    Compute multiple metrics for a batch of samples.

    Args:
        predictions: List of predicted texts
        references: List of reference texts
        metrics: List of metrics to compute (if None, compute all available metrics)
        include_per_sample: Whether to include per-sample metrics

    Returns:
        Dictionary of metric scores
    """
    # Compute per-sample metrics
    per_sample_metrics = [
        compute_metrics_for_sample(pred, ref, metrics)
        for pred, ref in zip(predictions, references)
    ]

    # Compute average metrics
    avg_metrics = {}
    for metric in per_sample_metrics[0].keys():
        values = [m[metric] for m in per_sample_metrics]
        avg_metrics[metric] = sum(values) / len(values)

    # Add BERTScore if available and requested
    if BERT_SCORE_AVAILABLE and (metrics is None or any(m.startswith('bert_') for m in metrics)):
        bert = bert_scores(predictions, references)
        for metric, values in bert.items():
            if metrics is None or metric in metrics:
                avg_metrics[metric] = sum(values) / len(values)
                if include_per_sample:
                    avg_metrics[f'{metric}_per_sample'] = values

    # Include per-sample metrics if requested
    if include_per_sample:
        for metric in per_sample_metrics[0].keys():
            avg_metrics[f'{metric}_per_sample'] = [m[metric] for m in per_sample_metrics]

    return avg_metrics
