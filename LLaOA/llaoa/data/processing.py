"""
Data processing and augmentation utilities for LLaOA.
"""

import os
import json
import random
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler

def load_rnaseq_data(
    file_path: str,
    sample_id_col: Optional[str] = None,
    set_index: bool = True
) -> pd.DataFrame:
    """
    Load RNAseq data from a file.
    
    Args:
        file_path: Path to the RNAseq data file (TSV/CSV)
        sample_id_col: Column name for sample ID
        set_index: Whether to set sample_id_col as index
        
    Returns:
        DataFrame with RNAseq data
    """
    # Determine file format
    if file_path.endswith('.tsv'):
        sep = '\t'
    elif file_path.endswith('.csv'):
        sep = ','
    else:
        raise ValueError(f"Unsupported file format: {file_path}")
    
    # Load data
    df = pd.read_csv(file_path, sep=sep)
    
    # Set index if requested
    if sample_id_col is not None and set_index:
        df.set_index(sample_id_col, inplace=True)
    
    return df

def load_qa_data(file_path: str) -> List[Dict[str, str]]:
    """
    Load QA data from a JSON file.
    
    Args:
        file_path: Path to the QA data file (JSON)
        
    Returns:
        List of QA pairs
    """
    with open(file_path, 'r') as f:
        qa_pairs = json.load(f)
    
    return qa_pairs

def normalize_rnaseq_data(
    df: pd.DataFrame,
    method: str = 'standard',
    log_transform: bool = True,
    pseudocount: float = 1.0
) -> pd.DataFrame:
    """
    Normalize RNAseq data.
    
    Args:
        df: DataFrame with RNAseq data
        method: Normalization method ('standard', 'minmax', 'robust')
        log_transform: Whether to apply log transformation
        pseudocount: Pseudocount to add before log transformation
        
    Returns:
        Normalized DataFrame
    """
    # Make a copy to avoid modifying the original
    df_norm = df.copy()
    
    # Apply log transformation if requested
    if log_transform:
        df_norm = np.log2(df_norm + pseudocount)
    
    # Apply normalization
    if method == 'standard':
        scaler = StandardScaler()
    elif method == 'minmax':
        scaler = MinMaxScaler()
    elif method == 'robust':
        scaler = RobustScaler()
    else:
        raise ValueError(f"Unsupported normalization method: {method}")
    
    # Normalize
    df_norm_values = scaler.fit_transform(df_norm)
    
    # Convert back to DataFrame
    df_norm = pd.DataFrame(df_norm_values, index=df_norm.index, columns=df_norm.columns)
    
    return df_norm

def filter_genes(
    df: pd.DataFrame,
    min_expression: float = 0.0,
    min_variance: float = 0.0,
    max_genes: Optional[int] = None
) -> pd.DataFrame:
    """
    Filter genes based on expression and variance.
    
    Args:
        df: DataFrame with RNAseq data
        min_expression: Minimum mean expression
        min_variance: Minimum variance
        max_genes: Maximum number of genes to keep
        
    Returns:
        Filtered DataFrame
    """
    # Calculate mean and variance
    gene_mean = df.mean(axis=0)
    gene_var = df.var(axis=0)
    
    # Filter by mean expression
    if min_expression > 0:
        df = df.loc[:, gene_mean >= min_expression]
    
    # Filter by variance
    if min_variance > 0:
        df = df.loc[:, gene_var >= min_variance]
    
    # Filter by number of genes
    if max_genes is not None and max_genes < df.shape[1]:
        # Sort genes by variance
        gene_var = df.var(axis=0)
        top_genes = gene_var.sort_values(ascending=False).index[:max_genes]
        df = df.loc[:, top_genes]
    
    return df

def augment_rnaseq_data(
    df: pd.DataFrame,
    method: str = 'noise',
    noise_level: float = 0.1,
    num_augmentations: int = 1,
    seed: int = 42
) -> Tuple[pd.DataFrame, List[str]]:
    """
    Augment RNAseq data.
    
    Args:
        df: DataFrame with RNAseq data
        method: Augmentation method ('noise', 'dropout', 'swap')
        noise_level: Level of noise/dropout/swap
        num_augmentations: Number of augmentations per sample
        seed: Random seed
        
    Returns:
        Tuple of (augmented DataFrame, list of augmented sample IDs)
    """
    # Set random seed
    np.random.seed(seed)
    random.seed(seed)
    
    # Make a copy to avoid modifying the original
    df_aug = df.copy()
    
    # Create augmented sample IDs
    aug_sample_ids = []
    
    # Apply augmentation
    for i, sample_id in enumerate(df.index):
        for j in range(num_augmentations):
            # Create augmented sample ID
            aug_id = f"{sample_id}_aug{j+1}"
            aug_sample_ids.append(aug_id)
            
            # Get sample data
            sample_data = df.iloc[i].values
            
            # Apply augmentation
            if method == 'noise':
                # Add Gaussian noise
                noise = np.random.normal(0, noise_level, size=sample_data.shape)
                aug_data = sample_data + noise
            elif method == 'dropout':
                # Randomly set some values to 0
                mask = np.random.random(sample_data.shape) < noise_level
                aug_data = sample_data.copy()
                aug_data[mask] = 0
            elif method == 'swap':
                # Randomly swap some values
                aug_data = sample_data.copy()
                num_swaps = int(noise_level * len(sample_data))
                for _ in range(num_swaps):
                    i1, i2 = np.random.choice(len(sample_data), 2, replace=False)
                    aug_data[i1], aug_data[i2] = aug_data[i2], aug_data[i1]
            else:
                raise ValueError(f"Unsupported augmentation method: {method}")
            
            # Add augmented sample to DataFrame
            df_aug.loc[aug_id] = aug_data
    
    return df_aug, aug_sample_ids

def augment_qa_data(
    qa_pairs: List[Dict[str, str]],
    original_to_augmented: Dict[str, List[str]]
) -> List[Dict[str, str]]:
    """
    Augment QA data to match augmented RNAseq data.

    Args:
        qa_pairs: List of QA pairs
        original_to_augmented: Mapping from original to augmented sample IDs

    Returns:
        Augmented QA pairs
    """
    # Make a copy to avoid modifying the original
    qa_pairs_aug = qa_pairs.copy()
    
    # Add augmented QA pairs
    for qa_pair in qa_pairs:
        sample_id = qa_pair['sample_id']
        if sample_id in original_to_augmented:
            for aug_id in original_to_augmented[sample_id]:
                # Create augmented QA pair
                aug_qa_pair = qa_pair.copy()
                aug_qa_pair['sample_id'] = aug_id
                qa_pairs_aug.append(aug_qa_pair)
    
    return qa_pairs_aug

def create_qa_variations(
    qa_pairs: List[Dict[str, str]],
    question_variations: Optional[List[str]] = None,
    answer_variations: Optional[List[str]] = None,
    seed: int = 42
) -> List[Dict[str, str]]:
    """
    Create variations of QA pairs with different question and answer phrasings.

    Note: This function replaces the deprecated create_prompt_templates() function.
    The streamlined LLaOA dataset now uses LLAMA2 chat templates exclusively,
    so we create content variations instead of format variations.

    Args:
        qa_pairs: List of QA pairs
        question_variations: List of question variation patterns (optional)
        answer_variations: List of answer variation patterns (optional)
        seed: Random seed

    Returns:
        QA pairs with content variations (no template fields)
    """
    # Set random seed
    random.seed(seed)

    # Default variations for questions and answers
    if question_variations is None:
        question_variations = [
            "{question}",  # Original
            "Can you help me understand: {question}",
            "I need to know: {question}",
            "Please analyze: {question}"
        ]

    if answer_variations is None:
        answer_variations = [
            "{answer}",  # Original
            "Based on the analysis: {answer}",
            "The data indicates: {answer}",
            "From the omics profile: {answer}"
        ]

    # Make a copy to avoid modifying the original
    qa_pairs_var = []

    # Create variations
    for qa_pair in qa_pairs:
        # Choose random variations
        question_var = random.choice(question_variations)
        answer_var = random.choice(answer_variations)

        # Create variation with modified content
        var_qa_pair = qa_pair.copy()
        var_qa_pair['question'] = question_var.format(question=qa_pair['question'])
        var_qa_pair['answer'] = answer_var.format(answer=qa_pair['answer'])

        # Remove any legacy template fields if they exist
        var_qa_pair.pop('prompt_template', None)
        var_qa_pair.pop('answer_prefix', None)

        qa_pairs_var.append(var_qa_pair)

    return qa_pairs_var

def validate_qa_pairs_for_llama2(qa_pairs: List[Dict[str, str]]) -> List[Dict[str, str]]:
    """
    Validate and clean QA pairs for LLAMA2 compatibility.

    Removes deprecated template fields and ensures required fields are present.

    Args:
        qa_pairs: List of QA pairs

    Returns:
        Cleaned QA pairs compatible with streamlined LLaOA dataset
    """
    cleaned_pairs = []

    for qa_pair in qa_pairs:
        # Create clean copy with only required fields
        clean_pair = {}

        # Required fields
        if 'sample_id' in qa_pair:
            clean_pair['sample_id'] = qa_pair['sample_id']
        else:
            raise ValueError("QA pair missing required 'sample_id' field")

        if 'question' in qa_pair:
            clean_pair['question'] = qa_pair['question']
        else:
            raise ValueError("QA pair missing required 'question' field")

        if 'answer' in qa_pair:
            clean_pair['answer'] = qa_pair['answer']
        else:
            raise ValueError("QA pair missing required 'answer' field")

        # Optional fields (preserve if present)
        for optional_field in ['cancer_type', 'tissue_type', 'metadata']:
            if optional_field in qa_pair:
                clean_pair[optional_field] = qa_pair[optional_field]

        # Remove deprecated fields (with warning)
        deprecated_fields = ['prompt_template', 'answer_prefix']
        for field in deprecated_fields:
            if field in qa_pair:
                print(f"Warning: Removing deprecated field '{field}' from QA pair. "
                      f"LLAMA2 chat template handles all formatting.")

        cleaned_pairs.append(clean_pair)

    return cleaned_pairs

def save_processed_data(
    rnaseq_df: pd.DataFrame,
    qa_pairs: List[Dict[str, str]],
    output_dir: str,
    rnaseq_filename: str = 'processed_rnaseq.tsv',
    qa_filename: str = 'processed_qa.json'
) -> Tuple[str, str]:
    """
    Save processed RNAseq and QA data.
    
    Args:
        rnaseq_df: DataFrame with RNAseq data
        qa_pairs: List of QA pairs
        output_dir: Output directory
        rnaseq_filename: Filename for RNAseq data
        qa_filename: Filename for QA data
        
    Returns:
        Tuple of (RNAseq file path, QA file path)
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Save RNAseq data
    rnaseq_path = os.path.join(output_dir, rnaseq_filename)
    rnaseq_df.to_csv(rnaseq_path, sep='\t')
    
    # Save QA data
    qa_path = os.path.join(output_dir, qa_filename)
    with open(qa_path, 'w') as f:
        json.dump(qa_pairs, f, indent=2)
    
    return rnaseq_path, qa_path
