import copy
import torch
import transformers
from typing import Dict, Sequence
from ..utils.conversation import conv_templates, SeparatorStyle
from ..utils.constants import IGNORE_INDEX, OMICS_TOKEN_INDEX
from ..utils.omics_utils import tokenizer_omics_token


def _tokenize_fn(strings: Sequence[str], tokenizer: transformers.PreTrainedTokenizer) -> Dict:
    """Tokenize a list of strings."""
    tokenized_list = [
        tokenizer(
            text,
            return_tensors="pt",
            padding="longest",
            max_length=tokenizer.model_max_length,
            truncation=True,
        ) for text in strings
    ]
    input_ids = labels = [
        tokenized.input_ids[0] for tokenized in tokenized_list
    ]
    input_ids_lens = labels_lens = [
        tokenized.input_ids.ne(tokenizer.pad_token_id).sum().item()
        for tokenized in tokenized_list
    ]
    return dict(
        input_ids=input_ids,
        labels=labels,
        input_ids_lens=input_ids_lens,
        labels_lens=labels_lens,
    )


def _mask_targets(target, tokenized_lens, speakers):
    """Mask targets for training - only compute loss on assistant responses."""
    # target: tensor of token IDs
    # tokenized_lens: list of lengths for each turn
    # speakers: list of speaker roles
    
    cur_idx = 0
    target[:cur_idx] = IGNORE_INDEX  # Mask any initial tokens
    
    for tokenized_len, speaker in zip(tokenized_lens, speakers):
        if speaker == "human":
            # Mask human/user tokens
            target[cur_idx:cur_idx + tokenized_len] = IGNORE_INDEX
        # Assistant tokens are kept for loss calculation
        cur_idx += tokenized_len


def preprocess_llama_2(
    sources,
    tokenizer: transformers.PreTrainedTokenizer,
    has_omics: bool = False
) -> Dict:
    """
    Preprocess conversations for LLAMA2 training with proper chat template.
    
    Args:
        sources: List of conversation sources
        tokenizer: LLAMA2 tokenizer
        has_omics: Whether the conversations contain omics data
    
    Returns:
        Dictionary with input_ids and labels
    """
    conv = conv_templates["llaoa_llama_2"].copy()
    roles = {"human": conv.roles[0], "gpt": conv.roles[1]}

    # Apply prompt templates
    conversations = []
    for i, source in enumerate(sources):
        # Handle both dictionary format (with 'conversations' key) and list format
        if isinstance(source, dict) and 'conversations' in source:
            conversation_list = source['conversations']
        else:
            conversation_list = source

        if roles[conversation_list[0]["from"]] != conv.roles[0]:
            # Skip the first one if it is not from human
            conversation_list = conversation_list[1:]

        conv.messages = []
        for j, sentence in enumerate(conversation_list):
            role = roles[sentence["from"]]
            assert role == conv.roles[j % 2], f"{i}"
            conv.append_message(role, sentence["value"])
        conversations.append(conv.get_prompt())

    # Tokenize conversations
    if has_omics:
        input_ids = torch.stack([tokenizer_omics_token(prompt, tokenizer, return_tensors='pt') for prompt in conversations], dim=0)
    else:
        input_ids = tokenizer(
            conversations,
            return_tensors="pt",
            padding="longest",
            max_length=tokenizer.model_max_length,
            truncation=True,
        ).input_ids

    targets = input_ids.clone()

    assert conv.sep_style == SeparatorStyle.LLAMA_2

    # Mask targets for LLAMA2 format
    # LLAMA2 format: <s>[INST] <<SYS>>system<</SYS>> user_msg [/INST] assistant_msg </s>
    for conversation, target in zip(conversations, targets):
        total_len = int(target.ne(tokenizer.pad_token_id).sum())

        # Split conversation by </s> to handle multi-turn
        rounds = conversation.split(conv.sep2)
        cur_len = 1  # Account for BOS token
        target[:cur_len] = IGNORE_INDEX  # Mask BOS token
        
        for i, rou in enumerate(rounds):
            if rou == "":
                break
                
            # Find [/INST] to separate instruction from response
            parts = rou.split("[/INST]")
            if len(parts) != 2:
                break
                
            # Mask instruction part (including [INST], system prompt, user message)
            instruction_part = parts[0] + "[/INST]"
            if has_omics:
                instruction_len = len(tokenizer_omics_token(instruction_part, tokenizer))
            else:
                instruction_len = len(tokenizer(instruction_part).input_ids)
            
            if i != 0:  # Not the first round, account for BOS token already counted
                instruction_len -= 1
                
            target[cur_len : cur_len + instruction_len] = IGNORE_INDEX
            cur_len += instruction_len
            
            # Keep response part for loss calculation
            response_part = parts[1]
            if has_omics:
                response_len = len(tokenizer_omics_token(response_part, tokenizer))
            else:
                response_len = len(tokenizer(response_part).input_ids)
            
            if i != 0:  # Not the first round
                response_len -= 1
                
            cur_len += response_len
            
        # Ensure we don't exceed the actual length
        target[cur_len:] = IGNORE_INDEX

    return dict(input_ids=input_ids, labels=targets)


def preprocess_conversations(
    sources: Sequence[str],
    tokenizer: transformers.PreTrainedTokenizer,
    has_omics: bool = False
) -> Dict:
    """
    Main preprocessing function that routes to appropriate conversation handler.
    
    Args:
        sources: List of conversation sources
        tokenizer: Tokenizer to use
        has_omics: Whether conversations contain omics data
    
    Returns:
        Dictionary with input_ids and labels
    """
    # Use LLAMA2 preprocessing for LLaOA
    return preprocess_llama_2(sources, tokenizer, has_omics=has_omics)


def format_qa_as_conversation(question: str, answer: str, has_omics: bool = False) -> Dict:
    """
    Format a question-answer pair as a conversation for LLAMA2 training.
    
    Args:
        question: User question
        answer: Assistant answer
        has_omics: Whether to include omics token in question
    
    Returns:
        Conversation dictionary
    """
    if has_omics and "<omics>" not in question:
        # Add omics token at the beginning of the question
        question = "<omics>\n" + question
    
    return {
        "conversations": [
            {"from": "human", "value": question},
            {"from": "gpt", "value": answer}
        ]
    }
