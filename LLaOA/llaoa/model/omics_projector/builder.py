"""
Omics projector builder following LLaVA's architecture principles.

Key improvements:
1. Direct projection from omics space to language model space (no compression bottleneck)
2. Proper weight initialization with conservative scaling for large dimensions
3. Architecture matching LLaVA's proven design patterns

Architecture comparison:
- LLaVA mlp2x_gelu: vision_features → lm_space → lm_space 
- LLaOA mlp2x_gelu: omics_features → lm_space → lm_space (fixed from compression approach)
"""
import torch.nn as nn
import torch
import re
import math

def _init_projector_weights(module):
    """Initialize projector weights with proper scaling for large dimension changes."""
    if isinstance(module, nn.Linear):
        # Use Xavier uniform initialization for better gradient flow
        # with scaling adjusted for large dimension changes
        fan_in = module.in_features
        fan_out = module.out_features
        
        # For very large input dimensions (like omics data), use conservative scaling
        if fan_in > 10000:  # Large omics feature space
            # Use smaller initialization scale to prevent saturation
            bound = math.sqrt(1.0 / fan_in)  # Conservative scaling
        else:
            # Standard Xavier initialization
            bound = math.sqrt(6.0 / (fan_in + fan_out))
        
        nn.init.uniform_(module.weight, -bound, bound)
        
        if module.bias is not None:
            nn.init.zeros_(module.bias)

def build_omics_projector(config, **kwargs):
    projector_type = getattr(config, 'omics_projector_type', 'linear')
    omics_hidden_size = getattr(config, 'omics_hidden_size', 128)
    lm_hidden_size = getattr(config, 'hidden_size', 4096)

    if projector_type == 'linear':
        projector = nn.Linear(omics_hidden_size, lm_hidden_size)
        _init_projector_weights(projector)
        return projector
    elif projector_type.startswith('mlp'):
        # Handle different MLP formats
        if '_gelu' in projector_type:
            # Format: mlp2x_gelu
            mlp_match = re.match(r'^mlp(\d+)x_gelu$', projector_type)
            if mlp_match:
                depth = int(mlp_match.group(1))
                if depth == 2:
                    # Follow LLaVA's approach: direct projection then same-dimension transformation
                    projector = nn.Sequential(
                        nn.Linear(omics_hidden_size, lm_hidden_size),
                        nn.GELU(),
                        nn.Linear(lm_hidden_size, lm_hidden_size)
                    )
                    projector.apply(_init_projector_weights)
                    return projector
                elif depth == 3:
                    # Follow LLaVA's approach: direct projection then same-dimension transformations
                    projector = nn.Sequential(
                        nn.Linear(omics_hidden_size, lm_hidden_size),
                        nn.GELU(),
                        nn.Linear(lm_hidden_size, lm_hidden_size),
                        nn.GELU(),
                        nn.Linear(lm_hidden_size, lm_hidden_size)
                    )
                    projector.apply(_init_projector_weights)
                    return projector
                else:
                    # Generic case - follow LLaVA pattern
                    layers = [nn.Linear(omics_hidden_size, lm_hidden_size)]
                    for _ in range(1, depth):
                        layers.append(nn.GELU())
                        layers.append(nn.Linear(lm_hidden_size, lm_hidden_size))
                    projector = nn.Sequential(*layers)
                    projector.apply(_init_projector_weights)
                    return projector
        else:
            # Format: mlp2x - follow LLaVA pattern
            depth = int(projector_type.replace('mlp', '').replace('x', ''))
            layers = [nn.Linear(omics_hidden_size, lm_hidden_size)]
            for _ in range(1, depth):
                layers.append(nn.ReLU())
                layers.append(nn.Linear(lm_hidden_size, lm_hidden_size))
            projector = nn.Sequential(*layers)
            projector.apply(_init_projector_weights)
            return projector
    else:
        raise ValueError(f'Unknown omics projector type: {projector_type}')