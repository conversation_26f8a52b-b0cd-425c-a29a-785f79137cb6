import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import List, Optional, Tuple, Union

from .omics_encoder.builder import build_omics_tower
from .omics_projector.builder import build_omics_projector
from ..utils.constants import IGNORE_INDEX, OMICS_TOKEN_INDEX, DEFAULT_OMICS_PATCH_TOKEN, DEFAULT_OMICS_START_TOKEN, DEFAULT_OMICS_END_TOKEN

class LlaoaMetaModel:
    """
    Base class for LLaOA models that integrates omics encoder with language models.
    Adapted from LLaVA's LlavaMetaModel architecture.
    """

    def __init__(self, config):
        super(LlaoaMetaModel, self).__init__(config)

        if hasattr(config, "omics_tower"):
            self.omics_tower = build_omics_tower(config.omics_tower)
            self.mm_projector = build_omics_projector(config)

            # For compatibility with different projector types
            self.omics_projector = self.mm_projector

    def get_omics_tower(self):
        """Get the omics tower (encoder) module."""
        omics_tower = getattr(self, 'omics_tower', None)
        if type(omics_tower) is list:
            omics_tower = omics_tower[0]
        return omics_tower

    def initialize_omics_modules(self, model_args, fsdp=None):
        """
        Initialize omics modules similar to LLaVA's initialize_vision_modules.

        Args:
            model_args: Model arguments containing omics configuration
            fsdp: FSDP configuration (optional)
        """
        omics_tower = model_args.omics_tower
        pretrain_mm_mlp_adapter = getattr(model_args, 'pretrain_mm_mlp_adapter', None)

        self.config.omics_tower = omics_tower

        if self.get_omics_tower() is None:
            omics_tower = build_omics_tower(model_args.omics_tower)

            if fsdp is not None and len(fsdp) > 0:
                self.omics_tower = [omics_tower]
            else:
                self.omics_tower = omics_tower
        else:
            if fsdp is not None and len(fsdp) > 0:
                omics_tower = self.omics_tower[0]
            else:
                omics_tower = self.omics_tower

        self.config.use_mm_proj = True
        self.config.omics_projector_type = getattr(model_args, 'omics_projector_type', 'linear')
        self.config.omics_hidden_size = omics_tower.hidden_size

        if getattr(self, 'mm_projector', None) is None:
            self.mm_projector = build_omics_projector(self.config)
        else:
            # In case it is frozen by LoRA
            for p in self.mm_projector.parameters():
                p.requires_grad = True

        if pretrain_mm_mlp_adapter is not None:
            mm_projector_weights = torch.load(pretrain_mm_mlp_adapter, map_location='cpu')
            def get_w(weights, keyword):
                return {k.split(keyword + '.')[1]: v for k, v in weights.items() if keyword in k}

            self.mm_projector.load_state_dict(get_w(mm_projector_weights, 'mm_projector'))

    def encode_omics(self, omics_data):
        """
        Encode omics data using the omics tower and project to language model dimension.

        Args:
            omics_data: torch.Tensor with RNAseq data

        Returns:
            torch.Tensor: Projected omics features
        """
        omics_features = self.get_omics_tower()(omics_data)

        # Ensure dtype consistency between omics tower output and projector
        # Convert to FP32 to match the projector's dtype
        if omics_features.dtype != torch.float32:
            omics_features = omics_features.to(dtype=torch.float32)

        omics_features = self.mm_projector(omics_features)
        return omics_features

    @property
    def device(self):
        """Get the device of the model."""
        return next(self.parameters()).device

    @property
    def dtype(self):
        """Get the dtype of the model."""
        return next(self.parameters()).dtype

    def prepare_inputs_labels_for_multimodal(
        self, input_ids, position_ids, attention_mask, past_key_values, labels, omics_data
    ):
        """
        Prepare inputs for the language model by incorporating omics features.
        Similar to LLaVA's image token handling but for omics data.

        Args:
            input_ids: Input token IDs with OMICS_TOKEN_INDEX placeholders
            position_ids: Position IDs
            attention_mask: Attention mask
            past_key_values: Past key values for efficient generation
            labels: Labels for computing loss
            omics_data: torch.Tensor with RNAseq data

        Returns:
            Tuple of processed inputs for the language model
        """
        omics_tower = self.get_omics_tower()
        if omics_tower is None or omics_data is None or input_ids.shape[1] == 1:
            return input_ids, position_ids, attention_mask, past_key_values, None, labels

        # Encode omics data
        omics_features = self.encode_omics(omics_data)

        # Let's just add dummy tensors if they do not exist,
        # it is a headache to deal with None all the time.
        _labels = labels
        _position_ids = position_ids
        _attention_mask = attention_mask
        if attention_mask is None:
            attention_mask = torch.ones_like(input_ids, dtype=torch.bool)
        else:
            attention_mask = attention_mask.bool()
        if position_ids is None:
            position_ids = torch.arange(0, input_ids.shape[1], dtype=torch.long, device=input_ids.device)
        if labels is None:
            labels = torch.full_like(input_ids, IGNORE_INDEX)

        # Remove the padding using attention_mask
        input_ids = [cur_input_ids[cur_attention_mask] for cur_input_ids, cur_attention_mask in zip(input_ids, attention_mask)]
        labels = [cur_labels[cur_attention_mask] for cur_labels, cur_attention_mask in zip(labels, attention_mask)]

        new_input_embeds = []
        new_labels = []
        cur_omics_idx = 0
        for batch_idx, cur_input_ids in enumerate(input_ids):
            num_omics = (cur_input_ids == OMICS_TOKEN_INDEX).sum()
            if num_omics == 0:
                cur_omics_features = omics_features[cur_omics_idx]
                cur_input_embeds_1 = self.embed_tokens(cur_input_ids)

                # Ensure dtype consistency between text and omics embeddings
                cur_omics_features = cur_omics_features.to(dtype=cur_input_embeds_1.dtype)

                cur_input_embeds = torch.cat([cur_input_embeds_1, cur_omics_features[0:0]], dim=0)
                new_input_embeds.append(cur_input_embeds)
                new_labels.append(labels[batch_idx])
                cur_omics_idx += 1
                continue

            omics_token_indices = [-1] + torch.where(cur_input_ids == OMICS_TOKEN_INDEX)[0].tolist() + [cur_input_ids.shape[0]]
            cur_input_ids_noomics = []
            cur_labels = labels[batch_idx]
            cur_labels_noomics = []
            for i in range(len(omics_token_indices) - 1):
                cur_input_ids_noomics.append(cur_input_ids[omics_token_indices[i]+1:omics_token_indices[i+1]])
                cur_labels_noomics.append(cur_labels[omics_token_indices[i]+1:omics_token_indices[i+1]])
            split_sizes = [x.shape[0] for x in cur_labels_noomics]
            cur_input_embeds = self.embed_tokens(torch.cat(cur_input_ids_noomics))
            cur_input_embeds_no_omics = torch.split(cur_input_embeds, split_sizes, dim=0)
            cur_new_input_embeds = []
            cur_new_labels = []

            for i in range(num_omics + 1):
                cur_new_input_embeds.append(cur_input_embeds_no_omics[i])
                cur_new_labels.append(cur_labels_noomics[i])
                if i < num_omics:
                    cur_omics_features = omics_features[cur_omics_idx]
                    cur_omics_idx += 1
                    cur_new_input_embeds.append(cur_omics_features)
                    cur_new_labels.append(torch.full((cur_omics_features.shape[0],), IGNORE_INDEX, device=cur_labels.device, dtype=cur_labels.dtype))

            # Ensure all embeddings are on the same device and have the same dtype
            target_dtype = cur_new_input_embeds[0].dtype  # Use the dtype of text embeddings as reference
            cur_new_input_embeds = [x.to(device=cur_input_ids.device, dtype=target_dtype) for x in cur_new_input_embeds]

            cur_new_input_embeds = torch.cat(cur_new_input_embeds)
            cur_new_labels = torch.cat(cur_new_labels)

            new_input_embeds.append(cur_new_input_embeds)
            new_labels.append(cur_new_labels)

        # Truncate sequences to max length as omics embeddings can make the sequence longer
        tokenizer_model_max_length = getattr(self.config, 'tokenizer_model_max_length', None)
        if tokenizer_model_max_length is not None:
            new_input_embeds = [x[:tokenizer_model_max_length] for x in new_input_embeds]
            new_labels = [x[:tokenizer_model_max_length] for x in new_labels]

        # Combine them
        max_len = max(x.shape[0] for x in new_input_embeds)
        batch_size = len(new_input_embeds)

        new_input_embeds_padded = []
        new_labels_padded = torch.full((batch_size, max_len), IGNORE_INDEX, dtype=new_labels[0].dtype, device=new_labels[0].device)
        attention_mask = torch.zeros((batch_size, max_len), dtype=attention_mask.dtype, device=attention_mask.device)
        position_ids = torch.zeros((batch_size, max_len), dtype=position_ids.dtype, device=position_ids.device)

        for i, (cur_new_embed, cur_new_labels) in enumerate(zip(new_input_embeds, new_labels)):
            cur_len = cur_new_embed.shape[0]
            if getattr(self.config, 'tokenizer_padding_side', 'right') == "left":
                new_input_embeds_padded.append(torch.cat((
                    torch.zeros((max_len - cur_len, cur_new_embed.shape[1]), dtype=cur_new_embed.dtype, device=cur_new_embed.device),
                    cur_new_embed
                ), dim=0))
                if cur_len > 0:
                    new_labels_padded[i, -cur_len:] = cur_new_labels
                    attention_mask[i, -cur_len:] = True
                    position_ids[i, -cur_len:] = torch.arange(0, cur_len, dtype=position_ids.dtype, device=position_ids.device)
            else:
                new_input_embeds_padded.append(torch.cat((
                    cur_new_embed,
                    torch.zeros((max_len - cur_len, cur_new_embed.shape[1]), dtype=cur_new_embed.dtype, device=cur_new_embed.device)
                ), dim=0))
                if cur_len > 0:
                    new_labels_padded[i, :cur_len] = cur_new_labels
                    attention_mask[i, :cur_len] = True
                    position_ids[i, :cur_len] = torch.arange(0, cur_len, dtype=position_ids.dtype, device=position_ids.device)

        new_input_embeds = torch.stack(new_input_embeds_padded, dim=0)

        if _labels is None:
            new_labels = None
        else:
            new_labels = new_labels_padded

        if _attention_mask is None:
            attention_mask = None
        else:
            attention_mask = attention_mask.to(dtype=_attention_mask.dtype)

        if _position_ids is None:
            position_ids = None

        return None, position_ids, attention_mask, past_key_values, new_input_embeds, new_labels

    def initialize_omics_tokenizer(self, model_args, tokenizer):
        """Initialize omics-related special tokens in the tokenizer."""
        if model_args.mm_use_omics_patch_token:
            tokenizer.add_tokens([DEFAULT_OMICS_PATCH_TOKEN], special_tokens=True)
            self.resize_token_embeddings(len(tokenizer))

        if model_args.mm_use_omics_start_end:
            num_new_tokens = tokenizer.add_tokens([DEFAULT_OMICS_START_TOKEN, DEFAULT_OMICS_END_TOKEN], special_tokens=True)
            self.resize_token_embeddings(len(tokenizer))

            if num_new_tokens > 0:
                input_embeddings = self.get_input_embeddings().weight.data
                output_embeddings = self.get_output_embeddings().weight.data

                input_embeddings_avg = input_embeddings[:-num_new_tokens].mean(
                    dim=0, keepdim=True)
                output_embeddings_avg = output_embeddings[:-num_new_tokens].mean(
                    dim=0, keepdim=True)

                input_embeddings[-num_new_tokens:] = input_embeddings_avg
                output_embeddings[-num_new_tokens:] = output_embeddings_avg

            if model_args.tune_mm_mlp_adapter:
                for p in self.get_input_embeddings().parameters():
                    p.requires_grad = True
                for p in self.get_output_embeddings().parameters():
                    p.requires_grad = False

            if model_args.pretrain_mm_mlp_adapter:
                mm_projector_weights = torch.load(model_args.pretrain_mm_mlp_adapter, map_location='cpu')
                embed_tokens_weight = mm_projector_weights['model.embed_tokens.weight']
                assert num_new_tokens == 2
                if input_embeddings.shape == embed_tokens_weight.shape:
                    input_embeddings[-num_new_tokens:] = embed_tokens_weight[-num_new_tokens:]
                elif embed_tokens_weight.shape[0] == num_new_tokens:
                    input_embeddings[-num_new_tokens:] = embed_tokens_weight
                else:
                    raise ValueError(f"Unexpected embed_tokens_weight shape. Pretrained: {embed_tokens_weight.shape}. Current: {input_embeddings.shape}. Number of new tokens: {num_new_tokens}.")
        elif model_args.mm_use_omics_patch_token:
            if model_args.tune_mm_mlp_adapter:
                for p in self.get_input_embeddings().parameters():
                    p.requires_grad = False
                for p in self.get_output_embeddings().parameters():
                    p.requires_grad = False


class LlaoaMetaForCausalLM(ABC):
    """
    Base class for LLaOA causal language models with omics integration.
    """

    @abstractmethod
    def get_model(self):
        """Return the underlying model."""
        pass

    def get_omics_tower(self):
        """Get the omics tower from the model."""
        return self.get_model().get_omics_tower()

    def encode_omics(self, omics_data):
        """Encode omics data using the model's omics tower."""
        return self.get_model().encode_omics(omics_data)

    def prepare_inputs_labels_for_multimodal(
        self, input_ids, position_ids, attention_mask, past_key_values, labels, omics_data
    ):
        """Prepare inputs for multimodal (omics + text) processing."""
        return self.get_model().prepare_inputs_labels_for_multimodal(
            input_ids, position_ids, attention_mask, past_key_values, labels, omics_data
        )