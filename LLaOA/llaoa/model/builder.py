import os
import torch
import transformers
import logging
from typing import Op<PERSON>, Dict, <PERSON>, <PERSON><PERSON>, Union
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoConfig

from .config import LlaoaConfig, OmicsTowerConfig
from .omics_encoder.builder import build_omics_tower
from .omics_projector.builder import build_omics_projector
from .language_model import (
    LlaoaLlamaForCausalLM, LlaoaLlamaConfig
)

# Set up logging
logger = logging.getLogger(__name__)

def get_model_name_from_path(model_path: str) -> str:
    """Extract model name from path."""
    if not model_path:
        return ""
    model_path = model_path.strip("/")
    return model_path.split("/")[-1] if "/" in model_path else model_path

def identify_model_type(config: Dict[str, Any]) -> str:
    """Identify the type of language model from config."""
    model_type = config.get("model_type", "").lower()

    if "llama" in model_type:
        return "llama"
    else:
        # Default to llama for LLaOA - only LLAMA models are supported
        logger.info(f"Model type '{model_type}' not explicitly recognized, defaulting to LLAMA")
        return "llama"

def get_model_class(model_type: str):
    """Get the appropriate LLaOA model class for the given model type."""
    # Only LLAMA models are supported in LLaOA
    return LlaoaLlamaForCausalLM

def get_config_class(model_type: str):
    """Get the appropriate LLaOA config class for the given model type."""
    # Only LLAMA models are supported in LLaOA
    return LlaoaLlamaConfig

def create_llaoa_config(
    base_config: AutoConfig,
    compass_model_path: str,
    feature_type: str,
    projector_type: str,
    seed: Optional[int] = None
) -> LlaoaConfig:
    """Create LLaOA config from base language model config."""
    # Identify model type and get appropriate config class
    model_type = identify_model_type(base_config.to_dict())
    logger.info(f"Identified model type: {model_type}")
    config_class = get_config_class(model_type)
    
    # Create omics tower config
    omics_tower_config = OmicsTowerConfig(
        model_path=compass_model_path,
        feature_type=feature_type,
        seed=seed
    )
    
    # Create LLaOA config by merging base config with omics components
    base_dict = base_config.to_dict()
    llaoa_config = config_class(
        omics_tower=omics_tower_config,
        omics_projector_type=projector_type,
        omics_hidden_size=omics_tower_config.hidden_size,
        **{k: v for k, v in base_dict.items() 
           if k not in ["omics_tower", "omics_projector_type", "omics_hidden_size"]}
    )
    
    return llaoa_config

def resolve_device_map(device_map: Union[str, Dict, None]) -> torch.device:
    """Resolve device mapping to a target device for omics components."""
    if device_map == "auto":
        return torch.device("cuda" if torch.cuda.is_available() else "cpu")
    elif isinstance(device_map, dict):
        devices = list(device_map.values())
        return torch.device(devices[0] if devices else "cpu")
    elif device_map is not None:
        return torch.device(device_map)
    else:
        return torch.device("cpu")

def add_omics_components(model, config: LlaoaConfig, target_device: torch.device):
    """Add omics tower and projector to the model with proper device placement."""
    if not hasattr(config, "omics_tower") or config.omics_tower is None:
        logger.warning("No omics tower config found - creating model without omics components")
        return None
    
    logger.info(f"Adding omics components on device: {target_device}")
    
    # Build and add omics tower - keep COMPASS in FP32 to preserve trained precision
    # COMPASS was trained in FP32 and converting to FP16 can cause numerical instability
    omics_tower = build_omics_tower(config.omics_tower, device=target_device, dtype=torch.float32)
    model.get_model().omics_tower = omics_tower
    
    # Build and add omics projector - use FP32 for consistency with language model
    # This avoids mixed precision gradient scaler issues during training
    omics_projector = build_omics_projector(config).to(target_device, dtype=torch.float32)
    model.get_model().omics_projector = omics_projector
    
    # Extract scaler for data preprocessing
    scaler = getattr(omics_tower, 'scaler', None)
    if scaler:
        logger.info("Extracted scaler from COMPASS model for omics data preprocessing")
    else:
        logger.warning("No scaler found in omics_tower")
    
    return scaler

def apply_device_mapping(model, device_map: Union[str, Dict, None]):
    """Apply device mapping to the complete model."""
    if device_map == "auto":
        if torch.cuda.is_available():
            model = model.cuda()
        else:
            logger.warning("CUDA not available, keeping model on CPU")
    elif isinstance(device_map, dict):
        from accelerate import dispatch_model
        dispatch_model(model, device_map=device_map)
    elif device_map is not None:
        model = model.to(device_map)
    
    return model

def load_pretrained_model(
    llaoa_model_path: str,
    model_name: Optional[str] = None,
    device_map: Union[str, Dict[str, Union[int, str]]] = "auto",
    compass_model_path: Optional[str] = None,
    feature_type: str = "gene_level",
    projector_type: str = "mlp2x_gelu",
    seed: Optional[int] = None,
    **kwargs
) -> Tuple[Any, Any, Any, int]:
    """
    Load a pretrained LLaOA model with omics capabilities or create a new one from a base language model.

    This function follows a clean, step-by-step approach:
    1. Set random seed
    2. Load tokenizer
    3. Create/load LLaOA configuration
    4. Load base language model
    5. Add omics components with proper device placement
    6. Apply device mapping to complete model
    7. Extract context length and return

    Args:
        llaoa_model_path: Path to LLaOA model or base language model
        model_name: Model name for configuration (optional)
        device_map: Device mapping strategy ("auto", device name, or custom dict)
        compass_model_path: Path to COMPASS omics model (required for new models)
        feature_type: COMPASS feature type ("gene_level", "geneset_level", "concept_level")
        projector_type: Omics projector type ("mlp2x_gelu", etc.)
        seed: Random seed for reproducibility

    Returns:
        Tuple of (tokenizer, model, scaler, context_len)
    """
    # Step 1: Set random seed for reproducibility
    if seed is not None:
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
        logger.info(f"Set random seed to {seed}")

    # Step 2: Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(llaoa_model_path, use_fast=False)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Add omics special tokens if they don't exist
    from ..utils.constants import DEFAULT_OMICS_TOKEN, DEFAULT_OMICS_PATCH_TOKEN
    special_tokens = [DEFAULT_OMICS_TOKEN, DEFAULT_OMICS_PATCH_TOKEN]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)

    if new_tokens:
        tokenizer.add_tokens(new_tokens, special_tokens=True)
        print(f"Added {len(new_tokens)} new omics tokens to tokenizer: {new_tokens}")
        # Note: Model token embeddings will be resized after model loading

    # Step 3: Create/load LLaOA configuration
    try:
        # Try to load existing LLaOA config
        config = AutoConfig.from_pretrained(llaoa_model_path)
        if not hasattr(config, "omics_tower"):
            raise ValueError("Not a LLaOA config")
        logger.info("Loaded existing LLaOA configuration")
    except Exception:
        # Create new LLaOA config from base language model
        logger.info("Creating new LLaOA config from base language model")
        if compass_model_path is None:
            raise ValueError("compass_model_path must be provided when creating a new LLaOA model")
        
        base_config = AutoConfig.from_pretrained(llaoa_model_path)
        config = create_llaoa_config(base_config, compass_model_path, feature_type, projector_type, seed)

    # Step 4: Load language model
    try:
        # Try to load as complete LLaOA model
        # Use FP32 to avoid mixed precision gradient scaler issues
        model = AutoModelForCausalLM.from_pretrained(
            llaoa_model_path,
            config=config,
            torch_dtype=torch.float32,
            device_map=None,  # Handle device mapping later
            **kwargs
        )
        logger.info("Loaded complete LLaOA model")
    except Exception:
        # Load as base language model and add omics components
        logger.info("Loading base language model and adding omics components")
        model_type = identify_model_type(config.to_dict())
        model_class = get_model_class(model_type)
        model = model_class.from_pretrained(
            llaoa_model_path,
            config=config,
            torch_dtype=torch.float32,
            device_map=None,  # Handle device mapping later
            **kwargs
        )

    # Step 5: Resize token embeddings if new tokens were added
    if new_tokens:
        model.resize_token_embeddings(len(tokenizer))
        logger.info(f"Resized token embeddings to accommodate {len(new_tokens)} new tokens")

    # Step 6: Add omics components with proper device placement
    target_device = resolve_device_map(device_map)
    scaler = add_omics_components(model, config, target_device)

    # Step 7: Apply device mapping to complete model
    model = apply_device_mapping(model, device_map)

    # Step 8: Get context length and return
    context_len = getattr(config, "max_position_embeddings", 2048)
    
    logger.info("LLaOA model loaded successfully")
    return tokenizer, model, scaler, context_len
