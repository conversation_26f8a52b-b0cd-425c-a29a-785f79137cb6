import torch
import torch.nn as nn
import pandas as pd
import sys
import os
from enum import Enum
from typing import Optional, Union, Dict, Any, Tuple
from pathlib import Path

# Add COMPASS to the Python path if needed
# Try multiple possible locations for COMPASS
possible_compass_paths = [
    # Default COMPASS model configuration
    '/opt/COMPASS',
    # When running from LLaOA/ directory - go up to parent of LLaOA/
    str(Path(__file__).parents[4] / 'COMPASS'),
    # When running from within the package - also try one level up
    str(Path(__file__).parents[3] / '../COMPASS'),
    # Direct parent directory
    str(Path(__file__).parent.parent.parent.parent.parent / 'COMPASS'),
    # Environment variable override
    os.environ.get('COMPASS_HOME', ''),
    # Fallback to relative paths
    '../COMPASS',
    './COMPASS'
]

# Add the first valid COMPASS path to sys.path
for compass_path in possible_compass_paths:
    if Path(compass_path).exists():
        if compass_path not in sys.path:
            sys.path.insert(0, compass_path)
        break

# Import COMPASS components
try:
    import compass
    from compass import loadcompass, PreTrainer, FineTuner
    from compass.dataloader import GeneData
    from compass.model.tune import worker_init_fn
    import torch.utils.data as Torchdata
    from tqdm import tqdm
except ImportError:
    raise ImportError(
        "Could not import COMPASS. Make sure it's installed and in your Python path. "
        "Tried the following paths: {}".format(possible_compass_paths)
    )

class CompassFeatureType(Enum):
    """Types of features that can be extracted from COMPASS."""
    GENE_LEVEL = "gene_level"  # 15,672 gene-level features
    GENESET_LEVEL = "geneset_level"  # 133 geneset-level features
    CONCEPT_LEVEL = "concept_level"  # 44 concept-level features

class COMPASSOmicsTower(nn.Module):
    """
    Wrapper for COMPASS model to use as an omics encoder in LLaOA with gradient flow support.

    This class wraps a COMPASS model and provides methods to extract different types
    of features from RNAseq data while maintaining gradient flow for training.
    """

    def __init__(
        self,
        encoder_config: Dict[str, Any],
        device: Optional[Union[str, torch.device]] = None,
        dtype: Optional[torch.dtype] = None
    ):
        """
        Initialize the COMPASS omics tower.

        Args:
            encoder_config: Configuration dictionary with the following keys:
                - model_path: Path to the COMPASS model
                - feature_type: Type of features to extract (default: "gene_level")
                - hidden_size: Hidden size of the output features (default: depends on feature_type)
                - trainable: Whether to make the encoder trainable (default: False)
                - seed: Random seed for reproducibility (default: None)
            device: Device to use for computation
            dtype: Data type to use for computation
        """
        super().__init__()

        # Get configuration parameters - handle both dict and config object
        if hasattr(encoder_config, 'model_path'):
            # Config object (OmicsTowerConfig)
            model_path = encoder_config.model_path
            feature_type_str = getattr(encoder_config, 'feature_type', 'gene_level')
            self.trainable = getattr(encoder_config, 'trainable', False)
            seed = getattr(encoder_config, 'seed', None)
        else:
            # Dictionary config
            model_path = encoder_config.get('model_path', None)
            feature_type_str = encoder_config.get('feature_type', 'gene_level')
            self.trainable = encoder_config.get('trainable', False)
            seed = encoder_config.get('seed', None)
            
        # Set seed if provided
        if seed is not None:
            import torch
            import numpy as np
            import random
            torch.manual_seed(seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed_all(seed)
            np.random.seed(seed)
            random.seed(seed)
            # Set deterministic behavior
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
            
        if model_path is None:
            raise ValueError('model_path must be provided in encoder_config for COMPASSOmicsTower')

        # Determine feature type
        try:
            self.feature_type = CompassFeatureType(feature_type_str)
        except ValueError:
            valid_types = [t.value for t in CompassFeatureType]
            raise ValueError(f"Invalid feature_type: {feature_type_str}. Must be one of {valid_types}")

        # Load COMPASS model
        self.compass_wrapper = loadcompass(model_path, map_location='cpu' if device is None else device)
        self.compass_model = self.compass_wrapper.model
        self.scaler = self.compass_wrapper.scaler

        # Set hidden size based on feature type
        if hasattr(encoder_config, 'model_path'):
            # Config object - use getattr with defaults
            if self.feature_type == CompassFeatureType.GENE_LEVEL:
                self.hidden_size = getattr(encoder_config, 'hidden_size', 15672)  # 15,672 genes
            elif self.feature_type == CompassFeatureType.GENESET_LEVEL:
                self.hidden_size = getattr(encoder_config, 'hidden_size', 133)  # 133 genesets
            elif self.feature_type == CompassFeatureType.CONCEPT_LEVEL:
                self.hidden_size = getattr(encoder_config, 'hidden_size', 44)  # 44 concepts
        else:
            # Dictionary config
            if self.feature_type == CompassFeatureType.GENE_LEVEL:
                self.hidden_size = encoder_config.get('hidden_size', 15672)  # 15,672 genes
            elif self.feature_type == CompassFeatureType.GENESET_LEVEL:
                self.hidden_size = encoder_config.get('hidden_size', 133)  # 133 genesets
            elif self.feature_type == CompassFeatureType.CONCEPT_LEVEL:
                self.hidden_size = encoder_config.get('hidden_size', 44)  # 44 concepts

        # Set device and dtype
        self._device = device or torch.device('cpu')
        # Keep COMPASS in FP32 to preserve trained precision - conversion to FP16 causes NaN loss
        self._dtype = dtype or torch.float32

        # Move COMPASS model to device and convert to correct dtype
        self.compass_model = self.compass_model.to(self._device, dtype=self._dtype)

        # Set model training mode based on trainable flag
        self.set_trainable(self.trainable)

    def set_trainable(self, trainable: bool):
        """
        Set whether the COMPASS model should be trainable.
        
        Args:
            trainable: If True, set model to training mode and enable gradients
        """
        self.trainable = trainable
        if trainable:
            self.compass_model.train()
            for param in self.compass_model.parameters():
                param.requires_grad = True
        else:
            self.compass_model.eval()
            for param in self.compass_model.parameters():
                param.requires_grad = False

    def forward(self, omics_data: torch.Tensor) -> torch.Tensor:
        """
        Extract features from RNAseq data using COMPASS.

        Args:
            omics_data: torch.Tensor with RNAseq data (already scaled), shape [batch_size, 1, num_genes]

        Returns:
            torch.Tensor: Extracted features, shape depends on feature_type:
                - GENE_LEVEL: [batch_size, 1, 15672]
                - GENESET_LEVEL: [batch_size, 1, 133]
                - CONCEPT_LEVEL: [batch_size, 1, 44]
        """
        if not isinstance(omics_data, torch.Tensor):
            raise ValueError('omics_data must be a torch.Tensor')

        # Remove the sequence dimension if present: [batch_size, 1, num_genes] -> [batch_size, num_genes]
        if omics_data.dim() == 3 and omics_data.size(1) == 1:
            omics_data = omics_data.squeeze(1)
        
        # Store original dtype to convert back later
        original_dtype = omics_data.dtype

        if self.trainable:
            # Use direct model forward pass to maintain gradients
            features_tensor = self._extract_features_trainable(omics_data)
        else:
            # Use original extraction method (eval mode)
            features_tensor = self._extract_features_eval(omics_data)

        # Add sequence dimension for compatibility: [batch, 1, features]
        if features_tensor.dim() == 2:
            features_tensor = features_tensor.unsqueeze(1)
        
        # Convert to FP16 for compatibility with language model while keeping COMPASS in FP32
        # This prevents numerical instability from FP32->FP16->FP32 conversions
        features_tensor = features_tensor.to(dtype=torch.float16)

        return features_tensor

    def _extract_features_trainable(self, omics_data: torch.Tensor) -> torch.Tensor:
        """
        Extract features in training mode maintaining gradient flow.
        
        This implements the logic from COMPASS Extractor function but with tensor operations
        that preserve gradients. Expects already-scaled tensor data.
        """
        # Ensure tensor is on the correct device and keep in FP32 for COMPASS
        omics_data = omics_data.to(device=self.device, dtype=torch.float32)
        
        # Forward pass through COMPASS model components
        # Following the logic from Extractor function but with tensor operations
        
        # Input encoder: Transform raw gene expression to embeddings
        encoding = self.compass_model.inputencoder(omics_data)
        
        # Extract different feature types based on configuration
        if self.feature_type == CompassFeatureType.GENE_LEVEL:
            # Gene-level features: use geneset scorer but extract gene-level projections
            genesetprojector = self.compass_model.latentprojector.genesetprojector
            gene_level_proj = genesetprojector.geneset_scorer(encoding)[:, 2:]  # remove pid, cancer
            features_tensor = gene_level_proj
            
        elif self.feature_type == CompassFeatureType.GENESET_LEVEL:
            # Geneset-level features
            geneset_level_proj, _ = self.compass_model.latentprojector(encoding)
            features_tensor = geneset_level_proj
            
        elif self.feature_type == CompassFeatureType.CONCEPT_LEVEL:
            # Concept-level (celltype/pathway) features
            _, cellpathway_level_proj = self.compass_model.latentprojector(encoding)
            features_tensor = cellpathway_level_proj
        
        return features_tensor

    def _extract_features_eval(self, omics_data: torch.Tensor) -> torch.Tensor:
        """
        Extract features in evaluation mode.
        Expects already-scaled tensor data.
        """
        # Use direct model processing for eval mode to maintain consistency
        with torch.no_grad():
            # Ensure tensor is on the correct device and keep in FP32 for COMPASS
            omics_data = omics_data.to(device=self.device, dtype=torch.float32)
            
            # Forward pass through COMPASS model components
            encoding = self.compass_model.inputencoder(omics_data)
            
            # Extract different feature types based on configuration
            if self.feature_type == CompassFeatureType.GENE_LEVEL:
                # Gene-level features: use geneset scorer but extract gene-level projections
                genesetprojector = self.compass_model.latentprojector.genesetprojector
                gene_level_proj = genesetprojector.geneset_scorer(encoding)[:, 2:]  # remove pid, cancer
                features_tensor = gene_level_proj
                
            elif self.feature_type == CompassFeatureType.GENESET_LEVEL:
                # Geneset-level features
                geneset_level_proj, _ = self.compass_model.latentprojector(encoding)
                features_tensor = geneset_level_proj
                
            elif self.feature_type == CompassFeatureType.CONCEPT_LEVEL:
                # Concept-level (celltype/pathway) features
                _, cellpathway_level_proj = self.compass_model.latentprojector(encoding)
                features_tensor = cellpathway_level_proj

        return features_tensor

    def to(self, device):
        """
        Move the COMPASS model to the specified device.
        This ensures proper device handling for the internal COMPASS model.
        
        Args:
            device: Target device
            
        Returns:
            Self for chaining
        """
        # Update internal device tracking
        if isinstance(device, str):
            device = torch.device(device)
        self._device = device
        
        # Move COMPASS model to device but keep in FP32 (self._dtype should be float32)
        self.compass_model = self.compass_model.to(device, dtype=torch.float32)
        
        # Call parent to() method
        super().to(device)
        
        return self

    @property
    def device(self) -> torch.device:
        """Get the device used by the model."""
        return self._device

    @property
    def dtype(self) -> torch.dtype:
        """Get the data type used by the model."""
        return self._dtype