from transformers import AutoConfig, PretrainedConfig
from dataclasses import dataclass, field
from typing import Dict, Optional, Union, Any, List

@dataclass
class OmicsTowerConfig:
    """
    Configuration for the omics tower (encoder) in LLaOA.

    Attributes:
        model_path: Path to the COMPASS model
        feature_type: Type of features to extract (default: "gene_level")
            Options: "gene_level", "geneset_level", "concept_level", "vector"
        hidden_size: Hidden size of the output features (default: depends on feature_type)
        seed: Random seed for reproducibility (default: None)
    """
    model_path: str
    feature_type: str = "gene_level"
    hidden_size: Optional[int] = None  # If None, will be set based on feature_type
    seed: Optional[int] = None  # Add seed parameter

    def __post_init__(self):
        """Set hidden_size based on feature_type if not provided."""
        if self.hidden_size is None:
            if self.feature_type == "gene_level":
                self.hidden_size = 15672  # 15,672 genes
            elif self.feature_type == "geneset_level":
                self.hidden_size = 133  # 133 genesets
            elif self.feature_type == "concept_level":
                self.hidden_size = 44  # 44 concepts
            elif self.feature_type == "vector":
                self.hidden_size = 32  # 32-dim vectors
            else:
                raise ValueError(f"Invalid feature_type: {self.feature_type}. "
                                 f"Must be one of: gene_level, geneset_level, concept_level, vector")

    def to_dict(self):
        """Convert to dictionary for serialization."""
        return {
            "model_path": self.model_path,
            "feature_type": self.feature_type,
            "hidden_size": self.hidden_size,
            "seed": self.seed  # Include seed in serialization
        }

class LlaoaConfig(PretrainedConfig):
    model_type = "llaoa"

    def __init__(
        self,
        omics_tower: Optional[Union[Dict[str, Any], OmicsTowerConfig]] = None,
        omics_projector_type: str = "mlp2x_gelu",
        omics_hidden_size: int = 15672,
        use_cache: bool = True,
        **kwargs
    ):
        self.omics_tower = omics_tower
        self.omics_projector_type = omics_projector_type
        self.omics_hidden_size = omics_hidden_size
        super().__init__(use_cache=use_cache, **kwargs)

    @classmethod
    def from_pretrained(cls, pretrained_model_name_or_path, **kwargs):
        config_dict = AutoConfig.from_pretrained(pretrained_model_name_or_path, **kwargs).to_dict()

        # Handle omics_tower config
        if "omics_tower" in config_dict and isinstance(config_dict["omics_tower"], dict):
            config_dict["omics_tower"] = OmicsTowerConfig(**config_dict["omics_tower"])

        return cls(**config_dict)

    def to_dict(self):
        output = super().to_dict()
        if hasattr(self, "omics_tower") and self.omics_tower is not None:
            if isinstance(self.omics_tower, OmicsTowerConfig):
                output["omics_tower"] = self.omics_tower.to_dict()
        return output
