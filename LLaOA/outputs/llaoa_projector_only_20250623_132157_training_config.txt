LLaOA Training Configuration
============================
Date: Mon Jun 23 13:21:57 EDT 2025
Host: usplscpg008.scp.astrazeneca.net
User: kvwr060
Working Directory: /projects/ods/us_eds/users/ai_hub/suraj_workspace/LLaOA/LLaOA
Run Name: llaoa_projector_only_20250623_132157

Model Configuration:
- COMPASS Model: ./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt
- Language Model: ./models/llama-2-7b-chat-hf
- Feature Type: gene_level
- Projector Type: mlp2x_gelu

Data Configuration:
- RNA-seq Data: ./data/dataset_01/rnaseq_tpm.tsv
- Q&A Data: ./data/dataset_01/qa_pairs.json
- Max Length: 1024
- Sample ID Column: sample_id

Training Configuration:
- Batch Size: 4
- Gradient Accumulation Steps: 4
- Learning Rate: 1e-4
- Number of Epochs: 3
- Eval Steps: 20
- Save Steps: 30
- Output Directory: ./outputs/llaoa_projector_only_20250623_132157/

Wandb Configuration:
- Enabled: true
- Project: llaoa_projector_only_20250623_132157 (auto-derived from run name)
- Run Name: llaoa_projector_only_20250623_132157
- Tags: projector-only,llama2,compass,genomics,tcga
- Notes: LLaOA projector-only training with wandb logging in offline mode
- Offline Mode: true
- Logs Directory: ./outputs/llaoa_projector_only_20250623_132157/logs/

Hardware:
- GPUs Detected: 4
- Device Map: auto
