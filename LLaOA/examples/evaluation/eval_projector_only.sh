#!/bin/bash

# LLaOA Evaluation Example Script - Projector Only Models
# This script demonstrates how to evaluate trained LLaOA models on genomics Q&A tasks
# with comprehensive metrics computation and result visualization.

set -e  # Exit on any error

# =============================================================================
# Configuration
# =============================================================================

# Model and data paths will be set based on current directory
# (see directory detection section below)
EXPERIMENT_NAME="llaoa_evaluation"

# Evaluation parameters
BATCH_SIZE=4
MAX_NEW_TOKENS=128
NUM_BEAMS=4
TEMPERATURE=0.7
TOP_P=0.9
MAX_LENGTH=512

# Generation settings
GENERATION_MODE=true
DO_SAMPLE=true
REPETITION_PENALTY=1.1

# =============================================================================
# Command Line Arguments
# =============================================================================

# Parse command line arguments
MODEL_PATH="$DEFAULT_MODEL_PATH"
HELP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--model-path)
            MODEL_PATH="$2"
            shift 2
            ;;
        -h|--help)
            HELP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Show help if requested
if [ "$HELP" = true ]; then
    cat << EOF
LLaOA Evaluation Script

Usage: $0 [OPTIONS]

Options:
    -m, --model-path PATH    Path to trained LLaOA model (default: $DEFAULT_MODEL_PATH)
    -h, --help              Show this help message

Examples:
    # Evaluate default model
    $0

    # Evaluate specific model
    $0 --model-path ./checkpoints/my_model

    # Evaluate model from training script
    $0 --model-path ./checkpoints/llaoa_projector_training_20241223_143022

Data Configuration:
    RNA-seq Data: $RNA_SEQ_PATH
    Q&A Data: $QA_JSON_PATH

Evaluation Settings:
    Batch Size: $BATCH_SIZE
    Max New Tokens: $MAX_NEW_TOKENS
    Generation Mode: $GENERATION_MODE
    Beam Search: $NUM_BEAMS beams
    Temperature: $TEMPERATURE
    Top-p: $TOP_P

Output:
    Results will be saved to: ./evaluation_results/[timestamp]/
EOF
    exit 0
fi

# =============================================================================
# Validation
# =============================================================================

echo "=== LLaOA Evaluation Script ==="
echo "Validating configuration..."

# Check if we're in the correct directory (either LLaOA root or LLaOA/LLaOA)
if [ ! -f "llaoa/__init__.py" ] && [ ! -f "LLaOA/llaoa/__init__.py" ]; then
    echo "Error: Please run this script from the LLaOA root directory or LLaOA/LLaOA directory"
    echo "Current directory: $(pwd)"
    echo "Looking for llaoa/__init__.py or LLaOA/llaoa/__init__.py"
    exit 1
fi

# Adjust paths based on current directory
if [ -f "llaoa/__init__.py" ]; then
    # We're in LLaOA/LLaOA directory
    DEFAULT_MODEL_PATH="../checkpoints/llaoa_projector_training_latest"
    RNA_SEQ_PATH="../data/dataset_01/rnaseq_tpm.tsv"
    QA_JSON_PATH="../data/dataset_01/qa_pairs.json"
    EVAL_OUTPUT_DIR="../evaluation_results/$(date +%Y%m%d_%H%M%S)"
else
    # We're in LLaOA root directory
    DEFAULT_MODEL_PATH="./checkpoints/llaoa_projector_training_latest"
    RNA_SEQ_PATH="./data/dataset_01/rnaseq_tpm.tsv"
    QA_JSON_PATH="./data/dataset_01/qa_pairs.json"
    EVAL_OUTPUT_DIR="./evaluation_results/$(date +%Y%m%d_%H%M%S)"
fi

# Check model path
if [ ! -d "$MODEL_PATH" ]; then
    echo "Error: Model not found at $MODEL_PATH"
    echo "Please ensure the model path is correct"
    echo "Available checkpoints:"
    find ./checkpoints -name "*.bin" -o -name "config.json" 2>/dev/null | head -10 || echo "  No checkpoints found"
    exit 1
fi

# Check for required model files
if [ ! -f "$MODEL_PATH/config.json" ]; then
    echo "Error: Model config not found at $MODEL_PATH/config.json"
    exit 1
fi

# Check data paths
if [ ! -f "$RNA_SEQ_PATH" ]; then
    echo "Error: RNA-seq data not found at $RNA_SEQ_PATH"
    exit 1
fi

if [ ! -f "$QA_JSON_PATH" ]; then
    echo "Error: Q&A data not found at $QA_JSON_PATH"
    exit 1
fi

echo "✓ All paths validated successfully"

# =============================================================================
# Environment Setup
# =============================================================================

echo "Setting up evaluation environment..."

# Create output directory
mkdir -p "$EVAL_OUTPUT_DIR"
echo "✓ Created output directory: $EVAL_OUTPUT_DIR"

# Log configuration
cat > "$EVAL_OUTPUT_DIR/evaluation_config.txt" << EOF
LLaOA Evaluation Configuration
==============================
Date: $(date)
Host: $(hostname)
User: $(whoami)
Working Directory: $(pwd)

Model Configuration:
- Model Path: $MODEL_PATH
- Model Type: LLaOA Projector-Only

Data Configuration:
- RNA-seq Data: $RNA_SEQ_PATH
- Q&A Data: $QA_JSON_PATH
- Max Length: $MAX_LENGTH
- Sample ID Column: sample_id

Evaluation Configuration:
- Batch Size: $BATCH_SIZE
- Generation Mode: $GENERATION_MODE
- Max New Tokens: $MAX_NEW_TOKENS
- Beam Search: $NUM_BEAMS beams
- Temperature: $TEMPERATURE
- Top-p: $TOP_P
- Do Sample: $DO_SAMPLE
- Repetition Penalty: $REPETITION_PENALTY
- Output Directory: $EVAL_OUTPUT_DIR

Hardware:
- GPUs Available: $(nvidia-smi -L | wc -l)
EOF

echo "✓ Configuration logged to $EVAL_OUTPUT_DIR/evaluation_config.txt"

# =============================================================================
# Evaluation
# =============================================================================

echo "Starting evaluation..."
echo "Model: $MODEL_PATH"
echo "Output: $EVAL_OUTPUT_DIR"

# Run evaluation with uv using the updated run_eval.py script
uv run python run_eval.py \
    --llaoa-checkpoint "$MODEL_PATH" \
    --rna-seq-path "$RNA_SEQ_PATH" \
    --qa-json-path "$QA_JSON_PATH" \
    --sample-id-col "sample_id" \
    --max-length $MAX_LENGTH \
    --output-dir "$EVAL_OUTPUT_DIR" \
    --per-device-eval-batch-size $BATCH_SIZE \
    --generation-mode \
    --max-new-tokens $MAX_NEW_TOKENS \
    --num-beams $NUM_BEAMS \
    --temperature $TEMPERATURE \
    --top-p $TOP_P \
    --repetition-penalty $REPETITION_PENALTY \
    --do-sample \
    --fp16 \
    --dataloader-num-workers 4 \
    --seed 42

# =============================================================================
# Post-evaluation Analysis
# =============================================================================

echo "Evaluation completed!"
echo "Analyzing results..."

# Check if results file exists
RESULTS_FILE="$EVAL_OUTPUT_DIR/results.json"
if [ -f "$RESULTS_FILE" ]; then
    echo "✓ Results saved to: $RESULTS_FILE"

    # Extract key metrics using Python
    python3 << EOF
import json
import os

results_file = "$RESULTS_FILE"
if os.path.exists(results_file):
    with open(results_file, 'r') as f:
        results = json.load(f)

    print("\n=== Evaluation Results Summary ===")

    # Print key metrics
    metrics = ['bleu', 'rouge1', 'rouge2', 'rougeL', 'exact_match', 'bert_precision', 'bert_recall', 'bert_f1']
    for metric in metrics:
        if metric in results:
            print(f"{metric.upper()}: {results[metric]:.4f}")

    print(f"\nTotal samples evaluated: {results.get('num_samples', 'N/A')}")
    print(f"Average generation length: {results.get('avg_generation_length', 'N/A')}")
else:
    print("Results file not found")
EOF

else
    echo "Warning: Results file not found at $RESULTS_FILE"
fi

# Create evaluation summary
cat > "$EVAL_OUTPUT_DIR/evaluation_summary.txt" << EOF
LLaOA Evaluation Summary
========================
Evaluation completed at: $(date)
Model evaluated: $MODEL_PATH
Results saved to: $EVAL_OUTPUT_DIR

Files Generated:
- results.json: Detailed evaluation metrics
- predictions.json: Model predictions and references
- evaluation_config.txt: Evaluation configuration
- evaluation_summary.txt: This summary

Visualizations (if generated):
- metrics_plot.png: Metrics visualization
- length_distribution.png: Generation length distribution
- sample_predictions.txt: Sample predictions for manual review

Next Steps:
1. Review detailed results in results.json
2. Analyze sample predictions in predictions.json
3. Compare with other models using the comparison tools
4. Fine-tune model if performance is not satisfactory

Model Performance Analysis:
- Check BLEU scores for fluency
- Check ROUGE scores for content overlap
- Check exact match for precision
- Check BERTScore for semantic similarity
- Review sample predictions for qualitative assessment
EOF

echo "✓ Evaluation summary saved to $EVAL_OUTPUT_DIR/evaluation_summary.txt"

# =============================================================================
# Results Display
# =============================================================================

echo ""
echo "=== Evaluation Complete ==="
echo "Model: $MODEL_PATH"
echo "Results: $EVAL_OUTPUT_DIR"
echo ""
echo "Key files:"
echo "  - $EVAL_OUTPUT_DIR/results.json (detailed metrics)"
echo "  - $EVAL_OUTPUT_DIR/predictions.json (model outputs)"
echo "  - $EVAL_OUTPUT_DIR/evaluation_summary.txt (summary)"
echo ""
echo "To view detailed results:"
echo "  cat $EVAL_OUTPUT_DIR/results.json | python -m json.tool"
echo ""
echo "To compare with other models:"
echo "  python -m llaoa.eval.compare_models --results-dir ./evaluation_results/"