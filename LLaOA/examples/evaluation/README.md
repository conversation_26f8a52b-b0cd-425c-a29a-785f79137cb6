# LLaOA Evaluation Examples

This directory contains comprehensive example scripts for evaluating trained LLaOA models on genomics Q&A tasks. The examples demonstrate how to run inference, compute metrics, and analyze model performance with proper result visualization.

## Overview

The evaluation examples show how to:

- Load and evaluate trained LLaOA models
- Run inference on genomics Q&A tasks with proper generation settings
- Compute comprehensive evaluation metrics (BLEU, ROUGE, BERTScore, Exact Match)
- Generate visualizations and analysis reports
- Compare model performance across different configurations
- Handle both classification and generation evaluation modes

## Available Scripts

### 1. Shell Script: `eval_projector_only.sh`

A comprehensive bash script for evaluating LLaOA models with automatic configuration and result analysis.

**Features:**
- Automatic model and data validation
- Configurable evaluation parameters
- Comprehensive metrics computation
- Result analysis and summary generation
- Support for both generation and classification modes

**Usage:**
```bash
# Evaluate default model
./examples/evaluation/eval_projector_only.sh

# Evaluate specific model
./examples/evaluation/eval_projector_only.sh --model-path ./checkpoints/my_model

# Show help
./examples/evaluation/eval_projector_only.sh --help
```

**Default Configuration:**
- **Model Path**: `./checkpoints/llaoa_projector_training_latest`
- **RNA-seq Data**: `./data/dataset_01/rnaseq_tpm.tsv`
- **Q&A Data**: `./data/dataset_01/qa_pairs.json`
- **Generation Mode**: Enabled
- **Batch Size**: 4
- **Max New Tokens**: 128
- **Beam Search**: 4 beams
- **Temperature**: 0.7

### 2. Python Script: `eval_llaoa_example.py`

A flexible Python script with advanced evaluation features and visualization capabilities.

**Features:**
- Comprehensive model validation and loading
- Advanced metrics computation and analysis
- Automatic visualization generation
- Detailed logging and error handling
- Performance analysis and recommendations
- Markdown report generation

**Usage:**
```bash
# Basic evaluation
uv run python examples/evaluation/eval_llaoa_example.py --model-path ./checkpoints/my_model

# Evaluation with visualizations
uv run python examples/evaluation/eval_llaoa_example.py --model-path ./checkpoints/my_model --visualize

# Custom configuration
uv run python examples/evaluation/eval_llaoa_example.py \
    --model-path ./checkpoints/my_model \
    --generation-mode \
    --batch-size 8 \
    --max-new-tokens 256 \
    --num-beams 4
```

## Evaluation Metrics

### Text Generation Metrics

1. **BLEU Score**: Measures n-gram overlap between generated and reference text
   - Range: 0-1 (higher is better)
   - Good for measuring fluency and precision

2. **ROUGE Scores**: Measures recall-oriented overlap
   - ROUGE-1: Unigram overlap
   - ROUGE-2: Bigram overlap
   - ROUGE-L: Longest common subsequence
   - Range: 0-1 (higher is better)

3. **BERTScore**: Semantic similarity using BERT embeddings
   - Precision, Recall, F1 scores
   - Better captures semantic meaning than n-gram metrics

4. **Exact Match**: Percentage of exactly matching responses
   - Range: 0-1 (higher is better)
   - Strict metric for precision

### Performance Analysis

The evaluation scripts provide:

- **Quantitative Metrics**: Numerical scores for model performance
- **Qualitative Analysis**: Sample predictions for manual review
- **Performance Insights**: Automated analysis of strengths and weaknesses
- **Recommendations**: Suggestions for model improvement

## Data Requirements

### Input Data Format

**RNA-seq Data** (`rnaseq_tpm.tsv`):
```
sample_id	cancer_code	A1BG	A1CF	A2M	...
TCGA-OR-A5JF	ACC	0.0517	0.2297	4.2702	...
TCGA-OR-A5L8	ACC	0.1234	0.5678	3.1415	...
```

**Q&A Data** (`qa_pairs.json`):
```json
[
  {
    "sample_id": "TCGA-OR-A5JF",
    "question": "To which cancer type does this RNAseq sample belong?",
    "answer": "This RNAseq sample belongs to the Adrenocortical carcinoma cancer type."
  }
]
```

### Model Requirements

The model directory should contain:
- `config.json`: Model configuration
- `pytorch_model.bin` or similar: Model weights
- Tokenizer files: For text processing
- Any additional model-specific files

## Evaluation Modes

### 1. Generation Mode (Recommended)

Evaluates the model's ability to generate free-form text responses.

**Advantages:**
- More realistic evaluation of model capabilities
- Captures fluency and coherence
- Better for real-world applications

**Configuration:**
```bash
--generation-mode \
--max-new-tokens 128 \
--num-beams 4 \
--temperature 0.7 \
--do-sample
```

### 2. Classification Mode

Evaluates the model using next-token prediction without generation.

**Advantages:**
- Faster evaluation
- More stable metrics
- Good for perplexity-based evaluation

**Configuration:**
```bash
# Classification mode (no --generation-mode flag)
--per-device-eval-batch-size 8
```

## Output Structure

After evaluation, the output directory contains:

```
evaluation_results/eval_YYYYMMDD_HHMMSS/
├── results.json                # Detailed evaluation metrics
├── predictions.json            # Model predictions and references
├── analysis.json              # Performance analysis and insights
├── evaluation_config.txt      # Evaluation configuration
├── evaluation_summary.txt     # Human-readable summary
├── evaluation_report.md       # Comprehensive markdown report
├── evaluation.log             # Detailed evaluation log
├── metrics_plot.png           # Performance visualization
└── sample_predictions.txt     # Sample outputs for review
```

## Understanding Results

### Key Metrics Interpretation

**BLEU Score:**
- > 0.3: High quality, fluent responses
- 0.1-0.3: Moderate quality, some fluency issues
- < 0.1: Low quality, significant problems

**ROUGE-L:**
- > 0.5: Good content overlap with references
- 0.3-0.5: Moderate content overlap
- < 0.3: Poor content overlap

**Exact Match:**
- > 0.5: High precision in responses
- 0.2-0.5: Moderate precision
- < 0.2: Low precision, responses may be imprecise

**BERTScore F1:**
- > 0.8: High semantic similarity
- 0.6-0.8: Moderate semantic similarity
- < 0.6: Low semantic similarity

### Sample Results Analysis

```json
{
  "bleu": 0.245,
  "rouge1": 0.456,
  "rouge2": 0.234,
  "rougeL": 0.398,
  "exact_match": 0.123,
  "bert_precision": 0.678,
  "bert_recall": 0.654,
  "bert_f1": 0.666,
  "num_samples": 1000,
  "avg_generation_length": 45.2
}
```

**Analysis:**
- Moderate BLEU score indicates reasonable fluency
- Good ROUGE scores show content overlap with references
- Low exact match suggests responses are paraphrased rather than exact
- High BERTScore indicates good semantic understanding

## Troubleshooting

### Common Issues

1. **Model Loading Errors**:
   ```bash
   # Check model files exist
   ls -la ./checkpoints/your_model/

   # Verify config.json is valid
   python -m json.tool ./checkpoints/your_model/config.json
   ```

2. **CUDA Out of Memory**:
   ```bash
   # Reduce batch size
   --per-device-eval-batch-size 2

   # Use CPU if necessary
   export CUDA_VISIBLE_DEVICES=""
   ```

3. **Data Format Issues**:
   ```bash
   # Check data format
   head -5 ./data/dataset_01/rnaseq_tpm.tsv
   python -m json.tool ./data/dataset_01/qa_pairs.json | head -20
   ```

4. **Missing Dependencies**:
   ```bash
   # Install missing packages
   uv add matplotlib seaborn nltk rouge-score bert-score
   ```

### Performance Issues

1. **Slow Evaluation**:
   - Increase batch size if memory allows
   - Reduce max_new_tokens for faster generation
   - Use fewer beams for beam search
   - Disable sampling for deterministic results

2. **Poor Metrics**:
   - Check if model was trained properly
   - Verify data preprocessing matches training
   - Consider different generation parameters
   - Review sample predictions manually

## Advanced Usage

### Batch Evaluation

Evaluate multiple models:

```bash
#!/bin/bash
models=("model1" "model2" "model3")
for model in "${models[@]}"; do
    echo "Evaluating $model..."
    ./examples/evaluation/eval_projector_only.sh --model-path ./checkpoints/$model
done
```

### Custom Metrics

Add custom evaluation metrics by modifying the evaluation scripts:

```python
def custom_metric(predictions, references):
    # Implement your custom metric
    scores = []
    for pred, ref in zip(predictions, references):
        # Calculate score for each prediction-reference pair
        score = your_metric_function(pred, ref)
        scores.append(score)
    return sum(scores) / len(scores)
```

### Comparative Analysis

Compare multiple models:

```bash
# Generate comparison report
python -c "
import json
import glob

results = {}
for file in glob.glob('./evaluation_results/*/results.json'):
    model_name = file.split('/')[-2]
    with open(file) as f:
        results[model_name] = json.load(f)

# Print comparison table
print('Model\tBLEU\tROUGE-L\tExact Match')
for model, metrics in results.items():
    print(f'{model}\t{metrics.get(\"bleu\", 0):.3f}\t{metrics.get(\"rougeL\", 0):.3f}\t{metrics.get(\"exact_match\", 0):.3f}')
"
```

## Best Practices

### Evaluation Strategy

1. **Use Generation Mode**: More realistic for real-world applications
2. **Multiple Runs**: Run evaluation multiple times with different seeds
3. **Sample Analysis**: Always review sample predictions manually
4. **Metric Combination**: Don't rely on a single metric
5. **Domain-Specific Metrics**: Consider genomics-specific evaluation criteria

### Generation Parameters

**For High Quality**:
```bash
--num-beams 4 \
--temperature 0.7 \
--top-p 0.9 \
--repetition-penalty 1.1
```

**For Speed**:
```bash
--num-beams 1 \
--do-sample false \
--max-new-tokens 64
```

**For Diversity**:
```bash
--temperature 1.0 \
--top-p 0.95 \
--do-sample true
```

## Integration with Training

### Continuous Evaluation

Set up automatic evaluation during training:

```bash
# Add to training script
--eval_steps 100 \
--evaluation_strategy steps \
--load_best_model_at_end true \
--metric_for_best_model eval_loss
```

### Model Selection

Use evaluation results for model selection:

1. Train multiple models with different hyperparameters
2. Evaluate each model using the same evaluation script
3. Compare results using the analysis tools
4. Select the best model based on target metrics

## Support

For evaluation issues:
1. Check the troubleshooting section above
2. Review evaluation logs for error messages
3. Verify model and data formats
4. Test with smaller datasets first
5. Consult the main LLaOA documentation