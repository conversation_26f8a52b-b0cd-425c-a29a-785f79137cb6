# LLaOA Examples

This directory contains comprehensive example scripts and documentation for training and evaluating LLaOA (Large Language and Omics Assistant) models on genomics Q&A tasks.

## Overview

LLaOA is a multimodal model that combines omics data (e.g., RNA-seq) with natural language processing for genomics question-answering tasks. These examples demonstrate:

- **End-to-end training workflows** using the latest LLaOA architecture
- **Proper LLAMA2 chat template formatting** with [INST]/[/INST] and <<SYS>>/</SYS> tokens
- **Label masking strategies** where instruction tokens are masked with IGNORE_INDEX (-100) for SFT training
- **Comprehensive evaluation** with multiple metrics and visualization
- **Best practices** for genomics Q&A model development

## Quick Start

### 1. Training a Model

```bash
# Using the shell script (recommended for beginners)
./examples/training/train_projector_only.sh

# Using the Python script with custom configuration
uv run python examples/training/train_llaoa_example.py --config examples/example_config.yaml
```

### 2. Evaluating a Model

```bash
# Using the shell script
./examples/evaluation/eval_projector_only.sh --model-path ./checkpoints/your_model

# Using the Python script with visualizations
uv run python examples/evaluation/eval_llaoa_example.py --model-path ./checkpoints/your_model --visualize
```

## Directory Structure

```
examples/
├── README.md                           # This file
├── example_config.yaml                 # Comprehensive configuration example
├── test_config.yaml                    # Minimal test configuration
├── training/
│   ├── README.md                       # Training documentation
│   ├── train_projector_only.sh         # Shell training script
│   └── train_llaoa_example.py          # Python training script
└── evaluation/
    ├── README.md                       # Evaluation documentation
    ├── eval_projector_only.sh          # Shell evaluation script
    └── eval_llaoa_example.py           # Python evaluation script
```

## Prerequisites

### 1. Environment Setup

```bash
# Install dependencies
uv sync

# Verify GPU availability (recommended)
nvidia-smi
```

### 2. Model Setup

Download and place the required models:

- **COMPASS Model**: `./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt`
- **LLaMA-2 Model**: `./models/llama-2-7b-chat-hf/`

### 3. Data Setup

Ensure your data follows the expected format:

- **RNA-seq Data**: `./data/dataset_01/rnaseq_tpm.tsv` (TSV format with sample_id column)
- **Q&A Data**: `./data/dataset_01/qa_pairs.json` (JSON format with sample_id, question, answer fields)

## Key Features

### Training Scripts

- **Automatic path validation** and environment setup
- **Flexible configuration** via YAML files or command-line arguments
- **Comprehensive logging** and progress tracking
- **Error handling** and troubleshooting guidance
- **Support for both projector-only and full model training**

### Evaluation Scripts

- **Multiple evaluation metrics** (BLEU, ROUGE, BERTScore, Exact Match)
- **Automatic visualization generation** for performance analysis
- **Detailed result analysis** with insights and recommendations
- **Support for both generation and classification modes**
- **Comprehensive reporting** in multiple formats

### Configuration Management

- **YAML-based configuration** for reproducible experiments
- **Extensive documentation** of all parameters
- **Example configurations** for different use cases
- **Validation and error checking** for all settings

## Architecture Overview

LLaOA consists of three main components:

1. **Omics Encoder (COMPASS)**: Processes RNA-seq data and extracts meaningful features
2. **Projector**: Maps omics features to the language model's embedding space
3. **Language Model (LLaMA-2)**: Generates responses based on projected omics features and text prompts

### Training Strategy

**Projector-Only Training** (Recommended):
- Freezes the omics encoder and language model weights
- Only trains the projector layer that maps omics features to language model space
- Faster training and lower memory requirements
- Good for initial alignment between modalities

## Data Processing

The scripts automatically handle:

1. **LLAMA2 Chat Template Formatting**:
   ```
   <s>[INST] <<SYS>>
   You are a helpful assistant specialized in genomics and omics data analysis.
   <</SYS>>

   {question} [/INST] {answer} </s>
   ```

2. **Label Masking Strategy**:
   - Instruction tokens (question part) are masked with IGNORE_INDEX (-100)
   - Only answer tokens contribute to the loss calculation
   - Ensures the model learns to generate appropriate responses

3. **Omics Token Integration**:
   - Special `<omics>` tokens are inserted to represent omics data
   - Omics features are projected and aligned with these tokens

## Usage Examples

### Basic Training

```bash
# Train with default settings
./examples/training/train_projector_only.sh
```

### Custom Training Configuration

```yaml
# Create custom_config.yaml
model:
  language_model_path: "./models/llama-2-7b-chat-hf"
  compass_model_path: "./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
  tune_projector_only: true

training:
  num_train_epochs: 5
  learning_rate: 2e-4
  per_device_train_batch_size: 8
```

```bash
# Train with custom configuration
uv run python examples/training/train_llaoa_example.py --config custom_config.yaml
```

### Comprehensive Evaluation

```bash
# Evaluate with visualizations and detailed analysis
uv run python examples/evaluation/eval_llaoa_example.py \
    --model-path ./checkpoints/my_model \
    --generation-mode \
    --visualize \
    --batch-size 8
```

## Best Practices

1. **Start Small**: Use the test configuration for initial experiments
2. **Monitor Training**: Watch for overfitting and adjust hyperparameters
3. **Evaluate Regularly**: Use multiple metrics to assess performance
4. **Document Experiments**: Keep track of configurations and results
5. **Validate Data**: Ensure data quality and format consistency

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or enable gradient checkpointing
2. **Model Not Found**: Verify model paths and downloads
3. **Data Format Issues**: Check RNA-seq and Q&A data formats
4. **Slow Training**: Enable mixed precision and optimize data loading

### Getting Help

1. Check the detailed README files in training/ and evaluation/ directories
2. Review the troubleshooting sections in the documentation
3. Examine the example configurations for guidance
4. Test with smaller datasets first

## Next Steps

After running the examples:

1. **Experiment with hyperparameters** to improve performance
2. **Try different projector architectures** (linear, mlp2x_gelu, mlp3x_gelu)
3. **Evaluate on your own datasets** by adapting the data format
4. **Implement custom metrics** for domain-specific evaluation
5. **Scale to larger models** and datasets as needed

## Support

For additional support:
- Review the comprehensive documentation in each subdirectory
- Check the configuration examples and templates
- Consult the main LLaOA documentation
- Verify system requirements and dependencies
