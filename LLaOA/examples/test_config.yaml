# Test configuration for LLaOA training
# This is a minimal configuration for testing the training scripts

model:
  language_model_path: "./models/llama-2-7b-chat-hf"
  compass_model_path: "./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
  feature_type: "gene_level"
  projector_type: "mlp2x_gelu"
  tune_projector_only: true

data:
  rna_seq_path: "./data/dataset_01/rnaseq_tpm.tsv"
  qa_json_path: "./data/dataset_01/qa_pairs.json"
  sample_id_col: "sample_id"
  max_length: 512
  train_split: 0.8
  val_split: 0.2

training:
  output_dir: "./checkpoints/test_training"
  num_train_epochs: 1  # Reduced for testing
  per_device_train_batch_size: 2  # Reduced for testing
  per_device_eval_batch_size: 2
  gradient_accumulation_steps: 2  # Reduced for testing
  learning_rate: 1e-4
  weight_decay: 0.01
  warmup_ratio: 0.1
  lr_scheduler_type: "cosine"
  logging_steps: 5  # More frequent for testing
  eval_steps: 50  # More frequent for testing
  save_steps: 100
  evaluation_strategy: "steps"
  save_strategy: "steps"
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  save_total_limit: 2  # Reduced for testing
  dataloader_num_workers: 2  # Reduced for testing
  fp16: true
  gradient_checkpointing: true
  report_to: "none"
  seed: 42
