# LLaOA Training Configuration Example
# This file demonstrates all available configuration options for LLaOA training
# Copy and modify this file to create your own training configurations

# =============================================================================
# Model Configuration
# =============================================================================
model:
  # Path to the language model (LLaMA-2)
  language_model_path: "./models/llama-2-7b-chat-hf"
  
  # Path to the COMPASS omics encoder model
  compass_model_path: "./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
  
  # Type of omics features to use
  # Options: "gene_level", "geneset_level", "concept_level"
  feature_type: "gene_level"
  
  # Type of projector to use for mapping omics features to language model space
  # Options: "linear", "mlp2x_gelu", "mlp3x_gelu"
  projector_type: "mlp2x_gelu"
  
  # Whether to only train the projector (recommended for initial training)
  # If true, freezes the omics encoder and language model weights
  tune_projector_only: true

# =============================================================================
# Data Configuration
# =============================================================================
data:
  # Path to RNA-seq data file (TSV format)
  rna_seq_path: "./data/dataset_01/rnaseq_tpm.tsv"
  
  # Path to Q&A pairs file (JSON format)
  qa_json_path: "./data/dataset_01/qa_pairs.json"
  
  # Column name for sample IDs in the RNA-seq data
  sample_id_col: "sample_id"
  
  # Maximum sequence length for input text
  max_length: 512
  
  # Data split ratios (should sum to 1.0)
  train_split: 0.8
  val_split: 0.2

# =============================================================================
# Training Configuration
# =============================================================================
training:
  # Output directory for saving model checkpoints and logs
  output_dir: "./checkpoints/llaoa_example_training"
  
  # Training hyperparameters
  num_train_epochs: 3
  per_device_train_batch_size: 4
  per_device_eval_batch_size: 4
  gradient_accumulation_steps: 4
  learning_rate: 1e-4
  weight_decay: 0.01
  warmup_ratio: 0.1
  
  # Learning rate scheduler
  # Options: "linear", "cosine", "cosine_with_restarts", "polynomial", "constant"
  lr_scheduler_type: "cosine"
  
  # Logging and evaluation
  logging_steps: 10
  eval_steps: 100
  save_steps: 200
  evaluation_strategy: "steps"  # Options: "no", "steps", "epoch"
  save_strategy: "steps"        # Options: "no", "steps", "epoch"
  
  # Model selection
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  
  # Checkpoint management
  save_total_limit: 3  # Keep only the best 3 checkpoints
  
  # Performance optimizations
  dataloader_num_workers: 4
  fp16: true                    # Use mixed precision training
  gradient_checkpointing: true  # Save memory at cost of speed
  
  # Experiment tracking
  report_to: "none"  # Options: "none", "tensorboard", "wandb"
  
  # Reproducibility
  seed: 42

# =============================================================================
# Advanced Configuration (Optional)
# =============================================================================

# Uncomment and modify these sections for advanced use cases

# # Custom tokenizer settings
# tokenizer:
#   padding_side: "right"
#   truncation_side: "right"
#   add_special_tokens: true

# # Custom data preprocessing
# preprocessing:
#   normalize_expression: true
#   log_transform: false
#   filter_low_expression: true
#   min_expression_threshold: 0.1

# # Multi-GPU training settings
# distributed:
#   backend: "nccl"
#   find_unused_parameters: false
#   gradient_as_bucket_view: true

# # Memory optimization
# memory:
#   max_memory_mb: 40000
#   cpu_offload: false
#   pin_memory: true

# # Generation settings for evaluation
# generation:
#   max_new_tokens: 128
#   num_beams: 4
#   temperature: 0.7
#   top_p: 0.9
#   do_sample: true
#   repetition_penalty: 1.1

# =============================================================================
# Configuration Notes
# =============================================================================

# 1. Model Paths:
#    - Ensure all model paths are correct and accessible
#    - Use relative paths from the LLaOA root directory
#    - Download models before training

# 2. Data Format:
#    - RNA-seq data should be in TSV format with sample_id column
#    - Q&A data should be in JSON format with sample_id, question, answer fields
#    - Ensure sample IDs match between RNA-seq and Q&A data

# 3. Training Strategy:
#    - Start with projector-only training (tune_projector_only: true)
#    - Use smaller batch sizes if you encounter memory issues
#    - Increase gradient_accumulation_steps to maintain effective batch size

# 4. Performance Tuning:
#    - Enable fp16 for faster training and lower memory usage
#    - Use gradient_checkpointing if memory is limited
#    - Adjust dataloader_num_workers based on your system

# 5. Monitoring:
#    - Set logging_steps to a reasonable value for monitoring
#    - Use eval_steps to track validation performance
#    - Enable report_to for experiment tracking (tensorboard/wandb)

# 6. Reproducibility:
#    - Set a fixed seed for reproducible results
#    - Use the same configuration for comparable experiments
