#!/usr/bin/env python3
"""
LLaOA Training Example Script

This script demonstrates how to train LLaOA models using the projector-only training mode.
It shows proper configuration of the model, data loading with LLAMA2 chat template formatting,
and training with label masking for supervised fine-tuning.

Usage:
    uv run python examples/training/train_llaoa_example.py [--config CONFIG_FILE]

Example:
    uv run python examples/training/train_llaoa_example.py
    uv run python examples/training/train_llaoa_example.py --config my_config.yaml
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

import torch
import yaml
from transformers import set_seed

# Add LLaOA to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from llaoa.train.train import train
from llaoa.data.omics_qa_dataset import OmicsQADataset
from llaoa.model.builder import load_pretrained_model

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LLaOATrainingConfig:
    """Configuration class for LLaOA training."""
    
    def __init__(self, config_dict: Optional[Dict[str, Any]] = None):
        """Initialize with default configuration, optionally override with config_dict."""
        
        # Generate run name for this training session
        run_name = f"llaoa_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Default configuration
        self.config = {
            # Model configuration
            "model": {
                "language_model_path": "./models/llama-2-7b-chat-hf",
                "compass_model_path": "./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt",
                "feature_type": "gene_level",
                "projector_type": "mlp2x_gelu",
                "tune_projector_only": True
            },
            
            # Data configuration
            "data": {
                "rna_seq_path": "./data/dataset_01/rnaseq_tpm.tsv",
                "qa_json_path": "./data/dataset_01/qa_pairs.json",
                "sample_id_col": "sample_id",
                "max_length": 512,
                "train_split": 0.8,
                "val_split": 0.2
            },
            
            # Training configuration
            "training": {
                "run_name": run_name,
                "output_dir": "./outputs",  # Base output directory
                "num_train_epochs": 3,
                "per_device_train_batch_size": 4,
                "per_device_eval_batch_size": 4,
                "gradient_accumulation_steps": 4,
                "learning_rate": 1e-4,
                "weight_decay": 0.01,
                "warmup_ratio": 0.1,
                "lr_scheduler_type": "cosine",
                "logging_steps": 10,
                "eval_steps": 100,
                "save_steps": 200,
                "evaluation_strategy": "steps",
                "save_strategy": "steps",
                "load_best_model_at_end": True,
                "metric_for_best_model": "eval_loss",
                "greater_is_better": False,
                "save_total_limit": 3,
                "dataloader_num_workers": 4,
                "fp16": True,
                "gradient_checkpointing": True,
                "report_to": "wandb",  # Use wandb by default
                "seed": 42
            },
            

        }
        
        # Override with provided config
        if config_dict:
            self._deep_update(self.config, config_dict)
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """Recursively update nested dictionary."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def save(self, path: str):
        """Save configuration to file."""
        with open(path, 'w') as f:
            yaml.dump(self.config, f, default_flow_style=False, indent=2)
    
    @classmethod
    def from_file(cls, path: str):
        """Load configuration from file."""
        with open(path, 'r') as f:
            config_dict = yaml.safe_load(f)
        return cls(config_dict)


def validate_paths(config: LLaOATrainingConfig) -> bool:
    """Validate that all required paths exist."""
    logger.info("Validating configuration paths...")
    
    # Check model paths
    compass_path = config.config["model"]["compass_model_path"]
    if not os.path.exists(compass_path):
        logger.error(f"COMPASS model not found: {compass_path}")
        return False
    
    language_model_path = config.config["model"]["language_model_path"]
    if not os.path.exists(language_model_path):
        logger.error(f"Language model not found: {language_model_path}")
        return False
    
    # Check data paths
    rna_seq_path = config.config["data"]["rna_seq_path"]
    if not os.path.exists(rna_seq_path):
        logger.error(f"RNA-seq data not found: {rna_seq_path}")
        return False
    
    qa_json_path = config.config["data"]["qa_json_path"]
    if not os.path.exists(qa_json_path):
        logger.error(f"Q&A data not found: {qa_json_path}")
        return False
    
    logger.info("✓ All paths validated successfully")
    return True


def setup_training_environment(config: LLaOATrainingConfig):
    """Set up the training environment."""
    logger.info("Setting up training environment...")
    
    # Set seed for reproducibility
    set_seed(config.config["training"]["seed"])
    
    # Get the run name and base output directory
    run_name = config.config["training"]["run_name"]
    base_output_dir = config.config["training"]["output_dir"]
    
    # Create the full output directory structure: <output-dir>/<run-name>/
    full_output_dir = os.path.join(base_output_dir, run_name)
    os.makedirs(full_output_dir, exist_ok=True)
    logger.info(f"✓ Created output directory: {full_output_dir}")
    logger.info(f"✓ Checkpoints will be saved to: {full_output_dir}/checkpoints/")
    logger.info(f"✓ Logs will be saved to: {full_output_dir}/logs/")
    
    # Save configuration
    config.save(os.path.join(full_output_dir, "training_config.yaml"))
    logger.info(f"✓ Saved configuration to {full_output_dir}/training_config.yaml")
    
    # Log system information
    info = {
        "timestamp": datetime.now().isoformat(),
        "run_name": run_name,
        "base_output_dir": base_output_dir,
        "full_output_dir": full_output_dir,
        "hostname": os.uname().nodename,
        "python_version": sys.version,
        "torch_version": torch.__version__,
        "cuda_available": torch.cuda.is_available(),
        "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
    }
    
    with open(os.path.join(full_output_dir, "system_info.json"), 'w') as f:
        json.dump(info, f, indent=2)
    
    logger.info(f"✓ System info saved to {full_output_dir}/system_info.json")


def create_training_args(config: LLaOATrainingConfig) -> list:
    """Create command line arguments for the training script."""
    args = []

    # Model arguments
    model_config = config.config["model"]
    args.extend([
        "--model-base", model_config["language_model_path"],
        "--compass-model-path", model_config["compass_model_path"],
        "--feature-type", model_config["feature_type"],
        "--projector-type", model_config["projector_type"]
    ])

    if model_config["tune_projector_only"]:
        args.append("--tune-projector-only")

    # Data arguments
    data_config = config.config["data"]
    args.extend([
        "--rna-seq-path", data_config["rna_seq_path"],
        "--qa-json-path", data_config["qa_json_path"],
        "--sample-id-col", data_config["sample_id_col"],
        "--max-length", str(data_config["max_length"])
    ])

    # Training arguments - convert underscores to hyphens for CLI
    training_config = config.config["training"]
    for key, value in training_config.items():
        # Skip validation_split as it's not needed for CLI
        if key in ["train_split", "val_split"]:
            continue
            
        cli_key = key.replace("_", "-")
        if isinstance(value, bool):
            if value:
                args.append(f"--{cli_key}")
        else:
            args.extend([f"--{cli_key}", str(value)])



    return args


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="LLaOA Training Example")
    parser.add_argument(
        "--config", 
        type=str, 
        help="Path to configuration file (YAML format)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Print configuration and exit without training"
    )
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config:
        logger.info(f"Loading configuration from {args.config}")
        config = LLaOATrainingConfig.from_file(args.config)
    else:
        logger.info("Using default configuration")
        config = LLaOATrainingConfig()
    
    # Validate configuration
    if not validate_paths(config):
        logger.error("Configuration validation failed")
        sys.exit(1)
    
    # Setup environment
    setup_training_environment(config)
    
    if args.dry_run:
        logger.info("Dry run mode - configuration saved, exiting without training")
        run_name = config.config["training"]["run_name"]
        base_output_dir = config.config["training"]["output_dir"]
        full_output_dir = os.path.join(base_output_dir, run_name)
        logger.info(f"Configuration saved to: {full_output_dir}")
        return
    
    # Create training arguments
    training_args = create_training_args(config)
    
    run_name = config.config["training"]["run_name"]
    logger.info("Starting training with the following configuration:")
    logger.info(f"Run Name: {run_name}")
    logger.info(f"Model: {config.config['model']['language_model_path']}")
    logger.info(f"COMPASS: {config.config['model']['compass_model_path']}")
    logger.info(f"Data: {config.config['data']['qa_json_path']}")
    logger.info(f"Output: {config.config['training']['output_dir']}/{run_name}/")
    logger.info(f"Report To: {config.config['training']['report_to']}")
    
    # Use subprocess to call run_train.py with the arguments
    import subprocess

    try:
        # Construct the command
        cmd = ["uv", "run", "python", "run_train.py"] + training_args

        logger.info(f"Running command: {' '.join(cmd)}")

        # Run training
        result = subprocess.run(cmd, check=True, capture_output=False)

        logger.info("✓ Training completed successfully!")
        full_output_dir = os.path.join(config.config['training']['output_dir'], run_name)
        logger.info(f"Model saved to: {full_output_dir}/checkpoints/")
        logger.info(f"Logs saved to: {full_output_dir}/logs/")

    except subprocess.CalledProcessError as e:
        logger.error(f"Training failed with return code {e.returncode}")
        raise
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()
