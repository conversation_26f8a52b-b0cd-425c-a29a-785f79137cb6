# LLaOA Training Examples

This directory contains comprehensive example scripts for training LLaOA models on genomics Q&A tasks. The examples demonstrate end-to-end training workflows using the latest LLaOA architecture with proper LLAMA2 chat template formatting and label masking strategies.

## Overview

LLaOA (Large Language and Omics Assistant) is a multimodal model that combines omics data (e.g., RNA-seq) with natural language processing for genomics question-answering tasks. The training examples show how to:

- Configure and train LLaOA models using projector-only training mode
- Implement proper LLAMA2 chat template formatting with [INST]/[/INST] and <<SYS>>/</SYS> tokens
- Apply label masking where instruction tokens are masked with IGNORE_INDEX (-100) for SFT training
- Handle RNA-seq data preprocessing and Q&A pair formatting
- Set up comprehensive training pipelines with proper logging and checkpointing
- Use TensorBoard integration with automatic directory organization

## Available Scripts

### 1. Shell Script: `train_projector_only.sh`

A comprehensive bash script that demonstrates the complete training workflow.

**Features:**
- Automatic path validation and environment setup
- Configurable hyperparameters and model paths
- Comprehensive logging and configuration tracking
- **TensorBoard integration** for experiment tracking
- **Organized directory structure**: `<output-dir>/<run-name>/checkpoints/` and `<output-dir>/<run-name>/logs/`
- Error handling and validation checks
- Post-training summary generation

**Usage:**
```bash
# Run from LLaOA root directory
./examples/training/train_projector_only.sh
```

**Configuration:**
The script uses the following default configuration:
- **COMPASS Model**: `./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt`
- **Language Model**: `./models/llama-2-7b-chat-hf`
- **RNA-seq Data**: `./data/dataset_01/rnaseq_tpm.tsv`
- **Q&A Data**: `./data/dataset_01/qa_pairs.json`
- **Training Mode**: Projector-only (freezes encoder and LM, trains only projector)
- **Batch Size**: 4 (with gradient accumulation steps of 4)
- **Learning Rate**: 1e-4
- **Epochs**: 3
- **Output Structure**: `./outputs/<run-name>/checkpoints/` and `./outputs/<run-name>/logs/`

### 2. Python Script: `train_llaoa_example.py`

A flexible Python script with configuration file support and advanced features.

**Features:**
- YAML configuration file support
- Comprehensive validation and error handling
- Flexible parameter configuration
- Dry-run mode for configuration testing
- Detailed logging and system information tracking
- **Simplified wandb integration** with automatic project naming
- **Organized output structure** with run-specific directories

**Usage:**
```bash
# Basic usage with default configuration
uv run python examples/training/train_llaoa_example.py

# Using custom configuration file
uv run python examples/training/train_llaoa_example.py --config my_config.yaml

# Dry run to test configuration
uv run python examples/training/train_llaoa_example.py --dry-run
```

**Configuration File Example:**
```yaml
model:
  language_model_path: "./models/llama-2-7b-chat-hf"
  compass_model_path: "./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
  feature_type: "gene_level"
  projector_type: "mlp2x_gelu"
  tune_projector_only: true

data:
  rna_seq_path: "./data/dataset_01/rnaseq_tpm.tsv"
  qa_json_path: "./data/dataset_01/qa_pairs.json"
  sample_id_col: "sample_id"
  max_length: 512

training:
  run_name: "my_experiment"
  output_dir: "./outputs"
  num_train_epochs: 3
  per_device_train_batch_size: 4
  gradient_accumulation_steps: 4
  learning_rate: 1e-4
  report_to: "wandb"

wandb:
  project: "my_genomics_project"  # Optional: defaults to run_name
  tags: "custom,experiment,genomics"
  notes: "Custom experiment with specific settings"
  offline: true
```

## Prerequisites

### 1. Model Setup

Download and place the required models:

```bash
# Create models directory
mkdir -p models

# Download COMPASS model (example - adjust URL as needed)
# Place at: models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt

# Download LLaMA-2 model (example - adjust path as needed)
# Place at: models/llama-2-7b-chat-hf/
```

### 2. Data Setup

Ensure your data follows the expected format:

**RNA-seq Data Format** (`rnaseq_tpm.tsv`):
```
sample_id	cancer_code	A1BG	A1CF	A2M	...
TCGA-OR-A5JF	ACC	0.0517	0.2297	4.2702	...
TCGA-OR-A5L8	ACC	0.1234	0.5678	3.1415	...
```

**Q&A Data Format** (`qa_pairs.json`):
```json
[
  {
    "sample_id": "TCGA-OR-A5JF",
    "question": "To which cancer type does this RNAseq sample belong?",
    "answer": "This RNAseq sample belongs to the Adrenocortical carcinoma cancer type."
  },
  {
    "sample_id": "TCGA-OR-A5JF",
    "question": "What are the highly expressed genes in this RNAseq sample?",
    "answer": "The highly expressed genes in this RNAseq sample are PEBP1, NPTX2, FTL, MT2A, AKR1B1, BSG, TMSB4X, ACTB."
  }
]
```

### 3. Environment Setup

```bash
# Install dependencies using uv
uv sync

# Verify GPU availability (optional but recommended)
nvidia-smi
```

## Training Process

### Architecture Overview

LLaOA consists of three main components:

1. **Omics Encoder (COMPASS)**: Processes RNA-seq data and extracts meaningful features
2. **Projector**: Maps omics features to the language model's embedding space
3. **Language Model (LLaMA-2)**: Generates responses based on projected omics features and text prompts

### Training Modes

**Projector-Only Training** (Recommended for initial training):
- Freezes the omics encoder and language model weights
- Only trains the projector layer that maps omics features to language model space
- Faster training and lower memory requirements
- Good for initial alignment between modalities

### Data Processing

The training scripts automatically handle:

1. **LLAMA2 Chat Template Formatting**:
   ```
   <s>[INST] <<SYS>>
   You are a helpful assistant specialized in genomics and omics data analysis.
   <</SYS>>

   {question} [/INST] {answer} </s>
   ```

2. **Label Masking Strategy**:
   - Instruction tokens (question part) are masked with IGNORE_INDEX (-100)
   - Only answer tokens contribute to the loss calculation
   - Ensures the model learns to generate appropriate responses

3. **Omics Token Integration**:
   - Special `<omics>` tokens are inserted to represent omics data
   - Omics features are projected and aligned with these tokens

## Output Structure

After training, the **new organized output directory structure** contains:

```
outputs/<run-name>/
├── checkpoints/               # Model checkpoints directory
│   ├── config.json           # Model configuration
│   ├── pytorch_model.bin     # Trained model weights
│   ├── tokenizer.json        # Tokenizer configuration
│   ├── tokenizer_config.json # Tokenizer settings
│   ├── training_args.bin     # Training arguments
│   └── trainer_state.json   # Training state
├── logs/                     # TensorBoard logs directory
│   └── tensorboard/         # TensorBoard run data
│       └── <run-name>/      # Individual run logs
├── training_config.yaml     # Training configuration
└── system_info.json         # System information
```

**Key Benefits:**
- ✅ Each training run has its own isolated directory
- ✅ Clean separation between checkpoints and logs
- ✅ No mixing of different experiment outputs
- ✅ Easy to manage and organize multiple runs

## TensorBoard Integration

The training scripts include **zero-configuration TensorBoard integration** for comprehensive experiment tracking:

### Simple Configuration

**Enable/Disable TensorBoard:**
- `--report-to tensorboard` → Enable TensorBoard (default)
- `--report-to none` → Disable TensorBoard

**Automatic Setup:**
- Logs automatically saved to `<output-dir>/<run-name>/logs/tensorboard/`
- Run name automatically derived from `--run-name` argument
- No additional configuration required

### Example Usage

```bash
python run_train.py \
    --report-to tensorboard \
    --run-name "my_run"
    # Automatically creates: ./outputs/my_run/checkpoints/ and ./outputs/my_run/logs/tensorboard/
```

### Shell Script Configuration

Edit the TensorBoard configuration section in `train_projector_only.sh`:

```bash
# TensorBoard Configuration
ENABLE_TENSORBOARD=true  # Enable/disable TensorBoard

# To disable TensorBoard:
# ENABLE_TENSORBOARD=false
```

### Python Script Configuration

In the YAML configuration file:

```yaml
training:
  run_name: "my_experiment"
  report_to: "tensorboard"  # or "none" to disable
```

### Features

- 📊 **Rich Metrics**: Training/validation loss, learning rate, system metrics
- 🎯 **Zero Configuration**: Automatic setup with sensible defaults
- 📁 **Organized Logs**: All logs saved to `<output-dir>/<run-name>/logs/tensorboard/`
- 🔧 **Full Compatibility**: Works with existing LLAMA2 and omics workflows
- 📈 **System Monitoring**: Real-time resource utilization tracking
- 🌐 **Web Interface**: Interactive visualizations through TensorBoard web UI

### View Results

```bash
# View TensorBoard dashboard
tensorboard --logdir outputs/<run-name>/logs/tensorboard/
# Open browser to http://localhost:6006
```

**Alternative port:**
```bash
tensorboard --logdir outputs/<run-name>/logs/tensorboard/ --port 8080
```

## Monitoring Training

### Key Metrics to Monitor

- **Training Loss**: Should decrease steadily
- **Evaluation Loss**: Should decrease without overfitting
- **Learning Rate**: Follows the scheduler (cosine by default)
- **GPU Memory Usage**: Monitor for memory issues
- **System Metrics**: CPU/memory usage (tracked by wandb)

### Using Logs

Monitor training progress through the console output and log files:

```bash
# Follow training progress
tail -f outputs/<run-name>/checkpoints/training.log
```

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**:
   - Reduce batch size: `--per-device-train-batch-size 2`
   - Increase gradient accumulation: `--gradient-accumulation-steps 8`
   - Enable gradient checkpointing: `--gradient-checkpointing true`

2. **Model Not Found**:
   - Verify model paths in the configuration
   - Ensure models are downloaded and placed correctly

3. **Data Format Issues**:
   - Check RNA-seq data has correct column names
   - Verify Q&A JSON format matches expected structure
   - Ensure sample IDs match between RNA-seq and Q&A data

4. **Slow Training**:
   - Enable mixed precision: `--fp16 true`
   - Increase number of dataloader workers: `--dataloader-num-workers 8`
   - Use multiple GPUs if available

5. **TensorBoard Issues**:
   - Script fails with TensorBoard errors: Set `ENABLE_TENSORBOARD=false` or `--report-to none`
   - Permission errors: Ensure write access to output directory
   - Port conflicts: Use `--port` argument with tensorboard command to specify different port

### Performance Optimization

1. **Memory Optimization**:
   ```bash
   # Use smaller batch size with more accumulation
   --per-device-train-batch-size 2 \
   --gradient-accumulation-steps 8
   ```

2. **Speed Optimization**:
   ```bash
   # Enable optimizations
   --fp16 true \
   --gradient-checkpointing true \
   --dataloader-num-workers 8
   ```

3. **Multi-GPU Training**:
   ```bash
   # Use accelerate for multi-GPU
   accelerate launch examples/training/train_llaoa_example.py
   ```

## Next Steps

After training:

1. **Evaluate the Model**:
   ```bash
   ./examples/evaluation/eval_projector_only.sh ./outputs/<run-name>/checkpoints/
   ```

2. **Run Inference**:
   ```bash
   uv run python run_eval.py --llaoa-checkpoint ./outputs/<run-name>/checkpoints/ --generation-mode
   ```

3. **Fine-tune Further**:
   - Adjust hyperparameters based on evaluation results
   - Try different learning rates or training strategies
   - Experiment with different projector architectures


### Custom Projector Types

Available projector types:
- `linear`: Simple linear projection
- `mlp2x_gelu`: Two-layer MLP with GELU activation (default)
- `mlp3x_gelu`: Three-layer MLP with GELU activation

### Custom Feature Types

Available feature types:
- `gene_level`: Gene-level features (default)
- `geneset_level`: Gene set-level features
- `concept_level`: Concept-level features

### Learning Rate Scheduling

Available schedulers:
- `cosine`: Cosine annealing (default)
- `linear`: Linear decay
- `constant`: Constant learning rate
- `polynomial`: Polynomial decay

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the training logs for error messages
3. Consult the main LLaOA documentation
4. Check GPU memory usage and system resources
5. Verify the new directory structure is correctly created