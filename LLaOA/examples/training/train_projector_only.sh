#!/bin/bash

# LLaOA Training Example Script - Projector Only Training
# This script demonstrates end-to-end training of LLaOA models using projector-only training mode
# which freezes the omics encoder and language model while training only the projector layer.
#
# Features:
# - Projector-only training mode (freezes encoder and language model)
# - TensorBoard integration for experiment tracking
# - Automatic directory organization: <output-dir>/<run-name>/checkpoints/ and <output-dir>/<run-name>/logs/
# - Comprehensive metrics logging (loss, learning rate, system metrics)
# - Compatible with existing LLAMA2 chat template and omics processing
# - Support for resuming training from checkpoints
#
# Usage:
#   ./train_projector_only.sh [train|resume] [checkpoint_path]
#
# Examples:
#   ./train_projector_only.sh train                    # Start new training
#   ./train_projector_only.sh resume checkpoint-60    # Resume from checkpoint-60
#   ./train_projector_only.sh resume ./outputs/llaoa_projector_only_20250623_140754/checkpoints/checkpoint-60

set -e  # Exit on any error

# =============================================================================
# Command Line Arguments Processing
# =============================================================================

# Default values
MODE="train"
CHECKPOINT_PATH=""

# Parse command line arguments
if [ $# -gt 0 ]; then
    MODE="$1"
fi

if [ $# -gt 1 ]; then
    CHECKPOINT_PATH="$2"
fi

# Validate mode
if [ "$MODE" != "train" ] && [ "$MODE" != "resume" ]; then
    echo "Error: Invalid mode '$MODE'. Use 'train' or 'resume'"
    echo ""
    echo "Usage: $0 [train|resume] [checkpoint_path]"
    echo ""
    echo "Examples:"
    echo "  $0 train                    # Start new training"
    echo "  $0 resume checkpoint-60     # Resume from checkpoint-60 in current outputs"
    echo "  $0 resume ./outputs/llaoa_projector_only_20250623_140754/checkpoints/checkpoint-60"
    echo ""
    exit 1
fi

# Validate checkpoint path for resume mode
if [ "$MODE" = "resume" ]; then
    if [ -z "$CHECKPOINT_PATH" ]; then
        echo "Error: Checkpoint path is required for resume mode"
        echo "Usage: $0 resume <checkpoint_path>"
        echo ""
        echo "Examples:"
        echo "  $0 resume checkpoint-60     # Resume from checkpoint-60 in current outputs"
        echo "  $0 resume ./outputs/llaoa_projector_only_20250623_140754/checkpoints/checkpoint-60"
        exit 1
    fi

    # If checkpoint path is just a checkpoint name (e.g., "checkpoint-60"),
    # try to find it in the most recent output directory
    if [[ "$CHECKPOINT_PATH" =~ ^checkpoint-[0-9]+$ ]]; then
        echo "Looking for checkpoint '$CHECKPOINT_PATH' in recent output directories..."

        # Find the most recent output directory
        RECENT_OUTPUT=$(find ./outputs -maxdepth 1 -name "llaoa_projector_only_*" -type d 2>/dev/null | sort -r | head -1)

        if [ -n "$RECENT_OUTPUT" ] && [ -d "$RECENT_OUTPUT/checkpoints/$CHECKPOINT_PATH" ]; then
            CHECKPOINT_PATH="$RECENT_OUTPUT/checkpoints/$CHECKPOINT_PATH"
            echo "Found checkpoint at: $CHECKPOINT_PATH"
        else
            echo "Error: Could not find checkpoint '$CHECKPOINT_PATH' in recent output directories"
            echo "Please provide the full path to the checkpoint"
            exit 1
        fi
    fi

    # Validate that checkpoint exists
    if [ ! -d "$CHECKPOINT_PATH" ]; then
        echo "Error: Checkpoint directory not found: $CHECKPOINT_PATH"
        exit 1
    fi

    # Extract run name and output directory from checkpoint path for resume
    if [[ "$CHECKPOINT_PATH" =~ .*/outputs/([^/]+)/checkpoints/checkpoint-[0-9]+$ ]]; then
        RESUME_RUN_NAME="${BASH_REMATCH[1]}"
        RESUME_OUTPUT_DIR="./outputs"
        echo "Resuming training for run: $RESUME_RUN_NAME"
    else
        echo "Warning: Could not extract run name from checkpoint path. Using default settings."
        RESUME_RUN_NAME=""
        RESUME_OUTPUT_DIR=""
    fi
fi

echo "=== LLaOA Training Script ==="
echo "Mode: $MODE"
if [ "$MODE" = "resume" ]; then
    echo "Checkpoint: $CHECKPOINT_PATH"
    if [ -n "$RESUME_RUN_NAME" ]; then
        echo "Resume run name: $RESUME_RUN_NAME"
    fi
fi
echo ""

# =============================================================================
# Configuration
# =============================================================================

# Model and data paths will be set based on current directory
# (see directory detection section below)

# Set run name based on mode
if [ "$MODE" = "resume" ] && [ -n "$RESUME_RUN_NAME" ]; then
    RUN_NAME="$RESUME_RUN_NAME"
    echo "Using existing run name: $RUN_NAME"
else
    RUN_NAME="llaoa_projector_only_$(date +%Y%m%d_%H%M%S)"
    echo "Generated new run name: $RUN_NAME"
fi

# Training hyperparameters
BATCH_SIZE=4
GRADIENT_ACCUMULATION_STEPS=4
LEARNING_RATE=1e-4
NUM_EPOCHS=3
MAX_LENGTH=1024
EVAL_STEPS=20 # small values for debugging and testing the code
SAVE_STEPS=30 # small values for debugging and testing the code
LOGGING_STEPS=10

# =============================================================================
# TensorBoard Configuration
# =============================================================================
# TensorBoard integration for experiment tracking
# - Logs saved to <output-dir>/<run-name>/logs/tensorboard/ directory
# - Project name defaults to run name
# - Comprehensive metrics: loss, learning rate, system metrics

ENABLE_TENSORBOARD=true  # Set to false to disable TensorBoard logging
TENSORBOARD_PROJECT=""   # Optional: will default to run name if empty
TENSORBOARD_TAGS="projector-only,llama2,compass,genomics,tcga"
TENSORBOARD_NOTES="LLaOA projector-only training with TensorBoard logging"

# To disable TensorBoard completely, set:
# ENABLE_TENSORBOARD=false

# To customize TensorBoard settings, modify:
# TENSORBOARD_PROJECT="my_custom_project"
# TENSORBOARD_TAGS="custom,tags,here"
# TENSORBOARD_NOTES="Custom notes about this experiment"

# Hardware configuration
NUM_GPUS=$(nvidia-smi -L | wc -l)
if [ $NUM_GPUS -eq 0 ]; then
    echo "Warning: No GPUs detected. Training will be very slow on CPU."
    DEVICE_MAP="cpu"
else
    echo "Detected $NUM_GPUS GPU(s)"
    DEVICE_MAP="auto"
    for i in $(seq 0 $((NUM_GPUS-1))); do
        GPU_IDS="$GPU_IDS,$i"
    done
    # GPU_IDS=${GPU_IDS:1}  # Remove leading comma    
    GPU_IDS="2,3"
    export CUDA_VISIBLE_DEVICES=$GPU_IDS
    echo "CUDA_VISIBLE_DEVICES set to $CUDA_VISIBLE_DEVICES"
fi

# =============================================================================
# Validation
# =============================================================================

echo "Validating configuration..."

# Check if we're in the correct directory (either LLaOA root or LLaOA/LLaOA)
if [ ! -f "llaoa/__init__.py" ] && [ ! -f "LLaOA/llaoa/__init__.py" ]; then
    echo "Error: Please run this script from the LLaOA root directory or LLaOA/LLaOA directory"
    echo "Current directory: $(pwd)"
    echo "Looking for llaoa/__init__.py or LLaOA/llaoa/__init__.py"
    exit 1
fi

# TODO: current directory is always LLaOA/LLaOA, so remove any assumptions about the directory structure and make changes accordingly
# Adjust paths based on current directory
if [ -f "llaoa/__init__.py" ]; then
    # We're in LLaOA/LLaOA directory
    COMPASS_MODEL_PATH="./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
    LANGUAGE_MODEL_PATH="./models/llama-2-7b-chat-hf"
    RNA_SEQ_PATH="./data/dataset_01/rnaseq_tpm.tsv"
    QA_JSON_PATH="./data/dataset_01/qa_pairs.json"
    if [ "$MODE" = "resume" ] && [ -n "$RESUME_OUTPUT_DIR" ]; then
        OUTPUT_DIR="$RESUME_OUTPUT_DIR"
    else
        OUTPUT_DIR="./outputs"
    fi
else
    # We're in LLaOA root directory
    COMPASS_MODEL_PATH="LLaOA/models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
    LANGUAGE_MODEL_PATH="LLaOA/models/llama-2-7b-chat-hf"
    RNA_SEQ_PATH="LLaOA/data/dataset_01/rnaseq_tpm.tsv"
    QA_JSON_PATH="LLaOA/data/dataset_01/qa_pairs.json"
    if [ "$MODE" = "resume" ] && [ -n "$RESUME_OUTPUT_DIR" ]; then
        OUTPUT_DIR="$RESUME_OUTPUT_DIR"
    else
        OUTPUT_DIR="LLaOA/outputs"
    fi
fi

# Check model paths
if [ ! -f "$COMPASS_MODEL_PATH" ]; then
    echo "Error: COMPASS model not found at $COMPASS_MODEL_PATH"
    echo "Please ensure the COMPASS model is downloaded and placed correctly"
    exit 1
fi

if [ ! -d "$LANGUAGE_MODEL_PATH" ]; then
    echo "Error: Language model not found at $LANGUAGE_MODEL_PATH"
    echo "Please ensure the LLaMA-2 model is downloaded and placed correctly"
    exit 1
fi

# Check data paths
if [ ! -f "$RNA_SEQ_PATH" ]; then
    echo "Error: RNA-seq data not found at $RNA_SEQ_PATH"
    exit 1
fi

if [ ! -f "$QA_JSON_PATH" ]; then
    echo "Error: Q&A data not found at $QA_JSON_PATH"
    exit 1
fi

echo "✓ All paths validated successfully"

# =============================================================================
# Environment Setup
# =============================================================================

echo "Setting up environment..."

# Create base output directory (run-specific directory will be created automatically)
mkdir -p "$OUTPUT_DIR"
echo "✓ Created base output directory: $OUTPUT_DIR"
echo "✓ Training run: $RUN_NAME"

if [ "$MODE" = "resume" ]; then
    echo "✓ Resuming from checkpoint: $CHECKPOINT_PATH"
    echo "✓ Existing checkpoints: $OUTPUT_DIR/$RUN_NAME/checkpoints/"
    echo "✓ Existing logs: $OUTPUT_DIR/$RUN_NAME/logs/"
else
    echo "✓ Checkpoints will be saved to: $OUTPUT_DIR/$RUN_NAME/checkpoints/"
    echo "✓ Logs will be saved to: $OUTPUT_DIR/$RUN_NAME/logs/"
fi

# Log configuration
LOG_CONFIG_FILE="$OUTPUT_DIR/${RUN_NAME}_training_config.txt"
cat > "$LOG_CONFIG_FILE" << EOF
LLaOA Training Configuration
============================
Date: $(date)
Host: $(hostname)
User: $(whoami)
Working Directory: $(pwd)
Run Name: $RUN_NAME

Model Configuration:
- COMPASS Model: $COMPASS_MODEL_PATH
- Language Model: $LANGUAGE_MODEL_PATH
- Feature Type: gene_level
- Projector Type: mlp2x_gelu

Data Configuration:
- RNA-seq Data: $RNA_SEQ_PATH
- Q&A Data: $QA_JSON_PATH
- Max Length: $MAX_LENGTH
- Sample ID Column: sample_id

Training Configuration:
- Batch Size: $BATCH_SIZE
- Gradient Accumulation Steps: $GRADIENT_ACCUMULATION_STEPS
- Learning Rate: $LEARNING_RATE
- Number of Epochs: $NUM_EPOCHS
- Eval Steps: $EVAL_STEPS
- Save Steps: $SAVE_STEPS
- Output Directory: $OUTPUT_DIR/$RUN_NAME/

TensorBoard Configuration:
- Enabled: $ENABLE_TENSORBOARD
- Project: ${TENSORBOARD_PROJECT:-$RUN_NAME} (auto-derived from run name)
- Run Name: $RUN_NAME
- Tags: $TENSORBOARD_TAGS
- Notes: $TENSORBOARD_NOTES
- Logs Directory: $OUTPUT_DIR/$RUN_NAME/logs/tensorboard/

Hardware:
- GPUs Detected: $NUM_GPUS
- Device Map: $DEVICE_MAP
EOF

echo "✓ Configuration logged to $LOG_CONFIG_FILE"

# =============================================================================
# Training
# =============================================================================

echo "Starting training..."
echo "Output will be saved to: $OUTPUT_DIR/$RUN_NAME/"

# Prepare TensorBoard arguments
TENSORBOARD_ARGS=""
if [ "$ENABLE_TENSORBOARD" = true ]; then
    echo "📊 TensorBoard logging enabled"
    echo "  Project: ${TENSORBOARD_PROJECT:-$RUN_NAME}"
    echo "  Run name: $RUN_NAME"
    echo "  Tags: $TENSORBOARD_TAGS"
    echo "  Logs directory: $OUTPUT_DIR/$RUN_NAME/logs/tensorboard/"

    TENSORBOARD_ARGS="--report-to tensorboard"

    if [ -n "$TENSORBOARD_PROJECT" ]; then
        TENSORBOARD_ARGS="$TENSORBOARD_ARGS --tensorboard-project \"$TENSORBOARD_PROJECT\""
    fi

    if [ -n "$TENSORBOARD_TAGS" ]; then
        TENSORBOARD_ARGS="$TENSORBOARD_ARGS --tensorboard-tags \"$TENSORBOARD_TAGS\""
    fi

    if [ -n "$TENSORBOARD_NOTES" ]; then
        TENSORBOARD_ARGS="$TENSORBOARD_ARGS --tensorboard-notes \"$TENSORBOARD_NOTES\""
    fi
else
    echo "📊 TensorBoard logging disabled"
    TENSORBOARD_ARGS="--report-to none"
fi

echo ""

# Prepare resume argument
RESUME_ARG=""
if [ "$MODE" = "resume" ]; then
    RESUME_ARG="--resume-from-checkpoint \"$CHECKPOINT_PATH\""
    echo "Adding resume argument: $RESUME_ARG"
fi

# Run training with uv using the updated run_train.py script
# Note: Using eval to properly handle quoted arguments
eval "uv run python run_train.py \
    --model-base \"$LANGUAGE_MODEL_PATH\" \
    --compass-model-path \"$COMPASS_MODEL_PATH\" \
    --feature-type \"gene_level\" \
    --projector-type \"mlp2x_gelu\" \
    --tune-projector-only \
    --rna-seq-path \"$RNA_SEQ_PATH\" \
    --qa-json-path \"$QA_JSON_PATH\" \
    --sample-id-col \"sample_id\" \
    --max-length $MAX_LENGTH \
    --run-name \"$RUN_NAME\" \
    --output-dir \"$OUTPUT_DIR\" \
    --num-train-epochs $NUM_EPOCHS \
    --per-device-train-batch-size $BATCH_SIZE \
    --gradient-accumulation-steps $GRADIENT_ACCUMULATION_STEPS \
    --learning-rate $LEARNING_RATE \
    --weight-decay 0.01 \
    --warmup-steps 100 \
    --lr-scheduler-type \"cosine\" \
    --logging-steps $LOGGING_STEPS \
    --eval-steps $EVAL_STEPS \
    --save-steps $SAVE_STEPS \
    --save-total-limit 3 \
    --dataloader-num-workers 4 \
    --fp16 \
    --seed 42 \
    $RESUME_ARG \
    $TENSORBOARD_ARGS"

# =============================================================================
# Post-training
# =============================================================================

echo "Training completed!"
echo "Results saved to: $OUTPUT_DIR/$RUN_NAME/"

# Create a summary
SUMMARY_FILE="$OUTPUT_DIR/$RUN_NAME/training_summary.txt"
cat > "$SUMMARY_FILE" << EOF
LLaOA Training Summary
=====================
Training completed at: $(date)
Run Name: $RUN_NAME
Final model saved to: $OUTPUT_DIR/$RUN_NAME/checkpoints/

Directory Structure:
- Checkpoints: $OUTPUT_DIR/$RUN_NAME/checkpoints/
- Logs: $OUTPUT_DIR/$RUN_NAME/logs/

TensorBoard Integration:
- Enabled: $ENABLE_TENSORBOARD
- Project: ${TENSORBOARD_PROJECT:-$RUN_NAME}
- Run name: $RUN_NAME
- Logs location: $OUTPUT_DIR/$RUN_NAME/logs/tensorboard/

Next Steps:
1. Evaluate the model using the evaluation script:
   ./examples/evaluation/eval_projector_only.sh $OUTPUT_DIR/$RUN_NAME/checkpoints/

2. Run inference on new data:
   uv run python run_eval.py --llaoa-checkpoint $OUTPUT_DIR/$RUN_NAME/checkpoints/ --generation-mode

3. Continue training (if needed):
   $0 resume checkpoint-60
   # or with full path:
   $0 resume $OUTPUT_DIR/$RUN_NAME/checkpoints/checkpoint-60

4. View TensorBoard logs (if enabled):
   tensorboard --logdir $OUTPUT_DIR/$RUN_NAME/logs/tensorboard/

Model Files:
- config.json: Model configuration
- pytorch_model.bin: Trained model weights
- tokenizer files: Tokenizer configuration
- training_args.bin: Training arguments
- trainer_state.json: Training state

Logs:
- ${LOG_CONFIG_FILE##*/}: Initial configuration
- training_summary.txt: This summary
- logs/: TensorBoard experiment logs (if enabled)
EOF

echo "✓ Training summary saved to $SUMMARY_FILE"
echo ""
echo "=== Training Complete ==="
echo "📁 Model saved to: $OUTPUT_DIR/$RUN_NAME/checkpoints/"

if [ "$ENABLE_TENSORBOARD" = true ]; then
    echo "📊 TensorBoard logs saved to: $OUTPUT_DIR/$RUN_NAME/logs/tensorboard/"
    echo ""
    echo "🔍 To view TensorBoard logs:"
    echo "  1. tensorboard --logdir $OUTPUT_DIR/$RUN_NAME/logs/tensorboard/"
    echo "  2. Open browser to http://localhost:6006"
    echo ""
fi

echo "📋 Next steps:"
echo "  1. Evaluate the model:"
echo "     ./examples/evaluation/eval_projector_only.sh $OUTPUT_DIR/$RUN_NAME/checkpoints/"
echo "  2. Run inference:"
echo "     uv run python run_eval.py --llaoa-checkpoint $OUTPUT_DIR/$RUN_NAME/checkpoints/ --generation-mode"
echo "  3. Resume training (if needed):"
echo "     $0 resume checkpoint-60"

if [ "$ENABLE_TENSORBOARD" = true ]; then
    echo ""
    echo "✨ Training completed with comprehensive TensorBoard logging! ✨"
else
    echo ""
    echo "✨ Training completed successfully! ✨"
fi