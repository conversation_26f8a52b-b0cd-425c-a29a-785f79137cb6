#!/bin/bash

# LLaOA Training Example Script - Projector Only Training
# This script demonstrates end-to-end training of LLaOA models using projector-only training mode
# which freezes the omics encoder and language model while training only the projector layer.
#
# Features:
# - Projector-only training mode (freezes encoder and language model)
# - Weights & Biases (wandb) integration with offline mode
# - Automatic directory organization: <output-dir>/<run-name>/checkpoints/ and <output-dir>/<run-name>/logs/
# - Comprehensive metrics logging (loss, learning rate, system metrics)
# - Compatible with existing LLAMA2 chat template and omics processing

set -e  # Exit on any error

# =============================================================================
# Configuration
# =============================================================================

# Model and data paths will be set based on current directory
# (see directory detection section below)
RUN_NAME="llaoa_projector_only_$(date +%Y%m%d_%H%M%S)"

# Training hyperparameters
BATCH_SIZE=4
GRADIENT_ACCUMULATION_STEPS=4
LEARNING_RATE=1e-4
NUM_EPOCHS=3
MAX_LENGTH=1024
EVAL_STEPS=20 # small values for debugging and testing the code
SAVE_STEPS=30 # small values for debugging and testing the code
LOGGING_STEPS=10

# =============================================================================
# Wandb Configuration
# =============================================================================
# Weights & Biases integration for experiment tracking
# - Runs in offline mode (no internet required)
# - Logs saved to <output-dir>/<run-name>/logs/ directory
# - Project name defaults to run name
# - Comprehensive metrics: loss, learning rate, system metrics

ENABLE_WANDB=true  # Set to false to disable wandb logging
WANDB_PROJECT=""   # Optional: will default to run name if empty
WANDB_TAGS="projector-only,llama2,compass,genomics,tcga"
WANDB_NOTES="LLaOA projector-only training with wandb logging in offline mode"

# To disable wandb completely, set:
# ENABLE_WANDB=false

# To customize wandb settings, modify:
# WANDB_PROJECT="my_custom_project"
# WANDB_TAGS="custom,tags,here"
# WANDB_NOTES="Custom notes about this experiment"

# Hardware configuration
NUM_GPUS=$(nvidia-smi -L | wc -l)
if [ $NUM_GPUS -eq 0 ]; then
    echo "Warning: No GPUs detected. Training will be very slow on CPU."
    DEVICE_MAP="cpu"
else
    echo "Detected $NUM_GPUS GPU(s)"
    DEVICE_MAP="auto"
    for i in $(seq 0 $((NUM_GPUS-1))); do
        GPU_IDS="$GPU_IDS,$i"
    done
    # GPU_IDS=${GPU_IDS:1}  # Remove leading comma    
    GPU_IDS="2,3"
    export CUDA_VISIBLE_DEVICES=$GPU_IDS
    echo "CUDA_VISIBLE_DEVICES set to $CUDA_VISIBLE_DEVICES"
fi

# =============================================================================
# Validation
# =============================================================================

echo "=== LLaOA Training Script ==="
echo "Validating configuration..."

# Check if we're in the correct directory (either LLaOA root or LLaOA/LLaOA)
if [ ! -f "llaoa/__init__.py" ] && [ ! -f "LLaOA/llaoa/__init__.py" ]; then
    echo "Error: Please run this script from the LLaOA root directory or LLaOA/LLaOA directory"
    echo "Current directory: $(pwd)"
    echo "Looking for llaoa/__init__.py or LLaOA/llaoa/__init__.py"
    exit 1
fi

# TODO: current directory is always LLaOA/LLaOA, so remove any assumptions about the directory structure and make changes accordingly
# Adjust paths based on current directory
if [ -f "llaoa/__init__.py" ]; then
    # We're in LLaOA/LLaOA directory
    COMPASS_MODEL_PATH="./models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
    LANGUAGE_MODEL_PATH="./models/llama-2-7b-chat-hf"
    RNA_SEQ_PATH="./data/dataset_01/rnaseq_tpm.tsv"
    QA_JSON_PATH="./data/dataset_01/qa_pairs.json"
    OUTPUT_DIR="./outputs"
else
    # We're in LLaOA root directory
    COMPASS_MODEL_PATH="LLaOA/models/compass-tcga-rnaseq-encoder/compass_tcga_pretrainer.pt"
    LANGUAGE_MODEL_PATH="LLaOA/models/llama-2-7b-chat-hf"
    RNA_SEQ_PATH="LLaOA/data/dataset_01/rnaseq_tpm.tsv"
    QA_JSON_PATH="LLaOA/data/dataset_01/qa_pairs.json"
    OUTPUT_DIR="LLaOA/outputs"
fi

# Check model paths
if [ ! -f "$COMPASS_MODEL_PATH" ]; then
    echo "Error: COMPASS model not found at $COMPASS_MODEL_PATH"
    echo "Please ensure the COMPASS model is downloaded and placed correctly"
    exit 1
fi

if [ ! -d "$LANGUAGE_MODEL_PATH" ]; then
    echo "Error: Language model not found at $LANGUAGE_MODEL_PATH"
    echo "Please ensure the LLaMA-2 model is downloaded and placed correctly"
    exit 1
fi

# Check data paths
if [ ! -f "$RNA_SEQ_PATH" ]; then
    echo "Error: RNA-seq data not found at $RNA_SEQ_PATH"
    exit 1
fi

if [ ! -f "$QA_JSON_PATH" ]; then
    echo "Error: Q&A data not found at $QA_JSON_PATH"
    exit 1
fi

echo "✓ All paths validated successfully"

# =============================================================================
# Environment Setup
# =============================================================================

echo "Setting up environment..."

# Create base output directory (run-specific directory will be created automatically)
mkdir -p "$OUTPUT_DIR"
echo "✓ Created base output directory: $OUTPUT_DIR"
echo "✓ Training run: $RUN_NAME"
echo "✓ Checkpoints will be saved to: $OUTPUT_DIR/$RUN_NAME/checkpoints/"
echo "✓ Logs will be saved to: $OUTPUT_DIR/$RUN_NAME/logs/"

# Log configuration
LOG_CONFIG_FILE="$OUTPUT_DIR/${RUN_NAME}_training_config.txt"
cat > "$LOG_CONFIG_FILE" << EOF
LLaOA Training Configuration
============================
Date: $(date)
Host: $(hostname)
User: $(whoami)
Working Directory: $(pwd)
Run Name: $RUN_NAME

Model Configuration:
- COMPASS Model: $COMPASS_MODEL_PATH
- Language Model: $LANGUAGE_MODEL_PATH
- Feature Type: gene_level
- Projector Type: mlp2x_gelu

Data Configuration:
- RNA-seq Data: $RNA_SEQ_PATH
- Q&A Data: $QA_JSON_PATH
- Max Length: $MAX_LENGTH
- Sample ID Column: sample_id

Training Configuration:
- Batch Size: $BATCH_SIZE
- Gradient Accumulation Steps: $GRADIENT_ACCUMULATION_STEPS
- Learning Rate: $LEARNING_RATE
- Number of Epochs: $NUM_EPOCHS
- Eval Steps: $EVAL_STEPS
- Save Steps: $SAVE_STEPS
- Output Directory: $OUTPUT_DIR/$RUN_NAME/

Wandb Configuration:
- Enabled: $ENABLE_WANDB
- Project: ${WANDB_PROJECT:-$RUN_NAME} (auto-derived from run name)
- Run Name: $RUN_NAME
- Tags: $WANDB_TAGS
- Notes: $WANDB_NOTES
- Offline Mode: true
- Logs Directory: $OUTPUT_DIR/$RUN_NAME/logs/

Hardware:
- GPUs Detected: $NUM_GPUS
- Device Map: $DEVICE_MAP
EOF

echo "✓ Configuration logged to $LOG_CONFIG_FILE"

# =============================================================================
# Training
# =============================================================================

echo "Starting training..."
echo "Output will be saved to: $OUTPUT_DIR/$RUN_NAME/"

# Prepare wandb arguments
WANDB_ARGS=""
if [ "$ENABLE_WANDB" = true ]; then
    echo "📊 Wandb logging enabled"
    echo "  Project: ${WANDB_PROJECT:-$RUN_NAME}"
    echo "  Run name: $RUN_NAME"
    echo "  Tags: $WANDB_TAGS"
    echo "  Offline mode: true"
    echo "  Logs directory: $OUTPUT_DIR/$RUN_NAME/logs/"

    WANDB_ARGS="--report-to wandb --wandb-offline"

    if [ -n "$WANDB_PROJECT" ]; then
        WANDB_ARGS="$WANDB_ARGS --wandb-project \"$WANDB_PROJECT\""
    fi

    if [ -n "$WANDB_TAGS" ]; then
        WANDB_ARGS="$WANDB_ARGS --wandb-tags \"$WANDB_TAGS\""
    fi

    if [ -n "$WANDB_NOTES" ]; then
        WANDB_ARGS="$WANDB_ARGS --wandb-notes \"$WANDB_NOTES\""
    fi
else
    echo "📊 Wandb logging disabled"
    WANDB_ARGS="--report-to none"
fi

echo ""

# Run training with uv using the updated run_train.py script
# Note: Using eval to properly handle quoted arguments
eval "uv run python run_train.py \
    --model-base \"$LANGUAGE_MODEL_PATH\" \
    --compass-model-path \"$COMPASS_MODEL_PATH\" \
    --feature-type \"gene_level\" \
    --projector-type \"mlp2x_gelu\" \
    --tune-projector-only \
    --rna-seq-path \"$RNA_SEQ_PATH\" \
    --qa-json-path \"$QA_JSON_PATH\" \
    --sample-id-col \"sample_id\" \
    --max-length $MAX_LENGTH \
    --run-name \"$RUN_NAME\" \
    --output-dir \"$OUTPUT_DIR\" \
    --num-train-epochs $NUM_EPOCHS \
    --per-device-train-batch-size $BATCH_SIZE \
    --gradient-accumulation-steps $GRADIENT_ACCUMULATION_STEPS \
    --learning-rate $LEARNING_RATE \
    --weight-decay 0.01 \
    --warmup-steps 100 \
    --lr-scheduler-type \"cosine\" \
    --logging-steps $LOGGING_STEPS \
    --eval-steps $EVAL_STEPS \
    --save-steps $SAVE_STEPS \
    --save-total-limit 3 \
    --dataloader-num-workers 4 \
    --fp16 \
    --seed 42 \
    $WANDB_ARGS"

# =============================================================================
# Post-training
# =============================================================================

echo "Training completed!"
echo "Results saved to: $OUTPUT_DIR/$RUN_NAME/"

# Create a summary
SUMMARY_FILE="$OUTPUT_DIR/$RUN_NAME/training_summary.txt"
cat > "$SUMMARY_FILE" << EOF
LLaOA Training Summary
=====================
Training completed at: $(date)
Run Name: $RUN_NAME
Final model saved to: $OUTPUT_DIR/$RUN_NAME/checkpoints/

Directory Structure:
- Checkpoints: $OUTPUT_DIR/$RUN_NAME/checkpoints/
- Logs: $OUTPUT_DIR/$RUN_NAME/logs/

Wandb Integration:
- Enabled: $ENABLE_WANDB
- Project: ${WANDB_PROJECT:-$RUN_NAME}
- Run name: $RUN_NAME
- Logs location: $OUTPUT_DIR/$RUN_NAME/logs/
- Offline mode: true (no internet required)

Next Steps:
1. Evaluate the model using the evaluation script:
   ./examples/evaluation/eval_projector_only.sh $OUTPUT_DIR/$RUN_NAME/checkpoints/

2. Run inference on new data:
   uv run python run_eval.py --llaoa-checkpoint $OUTPUT_DIR/$RUN_NAME/checkpoints/ --generation-mode

3. Continue training (if needed):
   Use the same command with --resume_from_checkpoint $OUTPUT_DIR/$RUN_NAME/checkpoints/

4. View wandb logs offline (if enabled):
   cd $OUTPUT_DIR/$RUN_NAME/logs/ && wandb offline

Model Files:
- config.json: Model configuration
- pytorch_model.bin: Trained model weights
- tokenizer files: Tokenizer configuration
- training_args.bin: Training arguments
- trainer_state.json: Training state

Logs:
- ${LOG_CONFIG_FILE##*/}: Initial configuration
- training_summary.txt: This summary
- logs/: Wandb experiment logs (if enabled)
EOF

echo "✓ Training summary saved to $SUMMARY_FILE"
echo ""
echo "=== Training Complete ==="
echo "📁 Model saved to: $OUTPUT_DIR/$RUN_NAME/checkpoints/"

if [ "$ENABLE_WANDB" = true ]; then
    echo "📊 Wandb logs saved to: $OUTPUT_DIR/$RUN_NAME/logs/"
    echo ""
    echo "🔍 To view wandb logs offline:"
    echo "  1. cd $OUTPUT_DIR/$RUN_NAME/logs/"
    echo "  2. wandb offline"
    echo "  3. Open browser to view dashboard"
    echo ""
fi

echo "📋 Next steps:"
echo "  1. Evaluate the model:"
echo "     ./examples/evaluation/eval_projector_only.sh $OUTPUT_DIR/$RUN_NAME/checkpoints/"
echo "  2. Run inference:"
echo "     uv run python run_eval.py --llaoa-checkpoint $OUTPUT_DIR/$RUN_NAME/checkpoints/ --generation-mode"

if [ "$ENABLE_WANDB" = true ]; then
    echo ""
    echo "✨ Training completed with comprehensive wandb logging! ✨"
else
    echo ""
    echo "✨ Training completed successfully! ✨"
fi